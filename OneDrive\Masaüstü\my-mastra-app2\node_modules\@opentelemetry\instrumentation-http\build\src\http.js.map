{"version": 3, "file": "http.js", "sourceRoot": "", "sources": ["../../src/http.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,4CAe4B;AAC5B,8CAQ6B;AAI7B,2BAA2B;AAE3B,uCAAoC;AACpC,oEAMwC;AACxC,mCAAsC;AACtC,8EAU6C;AAC7C,mCAiBiB;AAGjB;;GAEG;AACH,MAAa,mBAAoB,SAAQ,qCAA8C;IACrF,oCAAoC;IACnB,aAAa,GAAkB,IAAI,OAAO,EAAQ,CAAC;IAC5D,cAAc,CAAC;IAMf,iBAAiB,GAAqB,kCAAgB,CAAC,GAAG,CAAC;IAEnE,YAAY,SAAoC,EAAE;QAChD,KAAK,CAAC,qCAAqC,EAAE,iBAAO,EAAE,MAAM,CAAC,CAAC;QAC9D,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAClD,IAAI,CAAC,iBAAiB,GAAG,IAAA,yCAAuB,EAC9C,MAAM,EACN,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAC1C,CAAC;IACJ,CAAC;IAEkB,wBAAwB;QACzC,IAAI,CAAC,+BAA+B,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAC/D,sBAAsB,EACtB;YACE,WAAW,EAAE,iDAAiD;YAC9D,IAAI,EAAE,IAAI;YACV,SAAS,EAAE,eAAS,CAAC,MAAM;SAC5B,CACF,CAAC;QACF,IAAI,CAAC,+BAA+B,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAC/D,sBAAsB,EACtB;YACE,WAAW,EAAE,kDAAkD;YAC/D,IAAI,EAAE,IAAI;YACV,SAAS,EAAE,eAAS,CAAC,MAAM;SAC5B,CACF,CAAC;QACF,IAAI,CAAC,kCAAkC,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAClE,0DAAmC,EACnC;YACE,WAAW,EAAE,mCAAmC;YAChD,IAAI,EAAE,GAAG;YACT,SAAS,EAAE,eAAS,CAAC,MAAM;YAC3B,MAAM,EAAE;gBACN,wBAAwB,EAAE;oBACxB,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;oBAChE,GAAG,EAAE,EAAE;iBACR;aACF;SACF,CACF,CAAC;QACF,IAAI,CAAC,kCAAkC,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAClE,0DAAmC,EACnC;YACE,WAAW,EAAE,mCAAmC;YAChD,IAAI,EAAE,GAAG;YACT,SAAS,EAAE,eAAS,CAAC,MAAM;YAC3B,MAAM,EAAE;gBACN,wBAAwB,EAAE;oBACxB,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;oBAChE,GAAG,EAAE,EAAE;iBACR;aACF;SACF,CACF,CAAC;IACJ,CAAC;IAEO,qBAAqB,CAC3B,UAAkB,EAClB,aAAyB,EACzB,gBAA4B;QAE5B,IAAI,IAAI,CAAC,iBAAiB,GAAG,kCAAgB,CAAC,GAAG,EAAE;YACjD,iCAAiC;YACjC,IAAI,CAAC,+BAA+B,CAAC,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;SACxE;QAED,IAAI,IAAI,CAAC,iBAAiB,GAAG,kCAAgB,CAAC,MAAM,EAAE;YACpD,mCAAmC;YACnC,IAAI,CAAC,kCAAkC,CAAC,MAAM,CAC5C,UAAU,GAAG,IAAI,EACjB,gBAAgB,CACjB,CAAC;SACH;IACH,CAAC;IAEO,qBAAqB,CAC3B,UAAkB,EAClB,aAAyB,EACzB,gBAA4B;QAE5B,IAAI,IAAI,CAAC,iBAAiB,GAAG,kCAAgB,CAAC,GAAG,EAAE;YACjD,iCAAiC;YACjC,IAAI,CAAC,+BAA+B,CAAC,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;SACxE;QAED,IAAI,IAAI,CAAC,iBAAiB,GAAG,kCAAgB,CAAC,MAAM,EAAE;YACpD,mCAAmC;YACnC,IAAI,CAAC,kCAAkC,CAAC,MAAM,CAC5C,UAAU,GAAG,IAAI,EACjB,gBAAgB,CACjB,CAAC;SACH;IACH,CAAC;IAEQ,SAAS,CAAC,SAAoC,EAAE;QACvD,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACxB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;IACpD,CAAC;IAED,IAAI;QAIF,OAAO,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,IAAI,CAAC,uBAAuB,EAAE,CAAC,CAAC;IAC3E,CAAC;IAEO,uBAAuB;QAC7B,OAAO,IAAI,qDAAmC,CAC5C,MAAM,EACN,CAAC,GAAG,CAAC,EACL,CAAC,aAAmB,EAAQ,EAAE;YAC5B,MAAM,KAAK,GAAI,aAAqB,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,QAAQ,CAAC;YACtE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,qCAAqC,EAAE;gBAC3D,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAC/B,aAAa,EACb,SAAS,EACT,IAAI,CAAC,gCAAgC,CAAC,MAAM,CAAC,CACP,CAAC;gBACzC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAC3B,aAAa,EACb,KAAK,EACL,IAAI,CAAC,4BAA4B,CAAC,cAAc,CAAC,CAClD,CAAC;gBACF,IAAI,KAAK,EAAE;oBACT,iEAAiE;oBACjE,6CAA6C;oBAC5C,aAAqB,CAAC,OAAO,CAAC,OAAO,GAAG,cAAc,CAAC;oBACvD,aAAqB,CAAC,OAAO,CAAC,GAAG,GAAG,UAAU,CAAC;iBACjD;aACF;YACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,qCAAqC,EAAE;gBAC3D,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,MAAM,CAAC,SAAS,EAC9B,MAAM,EACN,IAAI,CAAC,gCAAgC,CAAC,MAAM,CAAC,CAC9C,CAAC;aACH;YACD,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,CAAC,aAAmB,EAAE,EAAE;YACtB,IAAI,aAAa,KAAK,SAAS;gBAAE,OAAO;YAExC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,qCAAqC,EAAE;gBAC3D,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;gBACvC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;aACpC;YACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,qCAAqC,EAAE;gBAC3D,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;aACtD;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAEO,wBAAwB;QAC9B,OAAO,IAAI,qDAAmC,CAC5C,OAAO,EACP,CAAC,GAAG,CAAC,EACL,CAAC,aAAoB,EAAS,EAAE;YAC9B,MAAM,KAAK,GAAI,aAAqB,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,QAAQ,CAAC;YACtE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,qCAAqC,EAAE;gBAC3D,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAC/B,aAAa,EACb,SAAS,EACT,IAAI,CAAC,qCAAqC,CAAC,OAAO,CAAC,CACb,CAAC;gBACzC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAC3B,aAAa,EACb,KAAK,EACL,IAAI,CAAC,iCAAiC,CAAC,cAAc,CAAC,CACvD,CAAC;gBACF,IAAI,KAAK,EAAE;oBACT,mEAAmE;oBACnE,6CAA6C;oBAC5C,aAAqB,CAAC,OAAO,CAAC,OAAO,GAAG,cAAc,CAAC;oBACvD,aAAqB,CAAC,OAAO,CAAC,GAAG,GAAG,UAAU,CAAC;iBACjD;aACF;YACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,qCAAqC,EAAE;gBAC3D,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,MAAM,CAAC,SAAS,EAC9B,MAAM,EACN,IAAI,CAAC,gCAAgC,CAAC,OAAO,CAAC,CAC/C,CAAC;aACH;YACD,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,CAAC,aAAoB,EAAE,EAAE;YACvB,IAAI,aAAa,KAAK,SAAS;gBAAE,OAAO;YAExC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,qCAAqC,EAAE;gBAC3D,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;gBACvC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;aACpC;YACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,qCAAqC,EAAE;gBAC3D,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;aACtD;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,gCAAgC,CAAC,SAA2B;QAClE,OAAO,CACL,QAAwD,EACS,EAAE;YACnE,OAAO,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC,CAAC;IACJ,CAAC;IAED;;;OAGG;IACK,gCAAgC,CAAC,SAA2B;QAClE,OAAO,CAAC,QAAkC,EAA4B,EAAE;YACtE,OAAO,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC,CAAC;IACJ,CAAC;IAEO,4BAA4B,CAClC,aAGuB;QAEvB,OAAO,CAAC,SAAmC,EAA4B,EAAE;YACvE,iEAAiE;YACjE,kEAAkE;YAClE,yEAAyE;YACzE,kEAAkE;YAClE,uEAAuE;YACvE,sEAAsE;YACtE,sEAAsE;YACtE,iCAAiC;YACjC,mFAAmF;YACnF,iHAAiH;YACjH,OAAO,SAAS,kBAAkB,CAEhC,OAAU,EAAE,GAAG,IAAqB;gBACpC,MAAM,GAAG,GAAG,aAAa,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;gBAC5C,GAAG,CAAC,GAAG,EAAE,CAAC;gBACV,OAAO,GAAG,CAAC;YACb,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED,sCAAsC;IAC9B,qCAAqC,CAAC,SAA2B;QACvE,OAAO,CAAC,QAAkC,EAA4B,EAAE;YACtE,MAAM,eAAe,GAAG,IAAI,CAAC;YAC7B,OAAO,SAAS,oBAAoB;YAClC,sEAAsE;YACtE,OAA4C,EAC5C,GAAG,IAAqB;gBAExB,wDAAwD;gBACxD,IACE,SAAS,KAAK,OAAO;oBACrB,OAAO,OAAO,KAAK,QAAQ;oBAC3B,OAAO,EAAE,WAAW,EAAE,IAAI,KAAK,KAAK,EACpC;oBACA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;oBACrC,eAAe,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;iBAC7C;gBACD,OAAO,eAAe,CAAC,gCAAgC,CAAC,SAAS,CAAC,CAChE,QAAQ,CACT,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;YACtB,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,kBAAkB,CAAC,OAA6B;QACtD,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,QAAQ,CAAC;QAChD,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,GAAG,CAAC;IACrC,CAAC;IAED,0CAA0C;IAClC,iCAAiC,CACvC,aAIuB;QAEvB,OAAO,CAAC,QAAkC,EAA4B,EAAE;YACtE,MAAM,eAAe,GAAG,IAAI,CAAC;YAC7B,OAAO,SAAS,oBAAoB;YAClC,sEAAsE;YACtE,OAA4C,EAC5C,GAAG,IAAqB;gBAExB,OAAO,eAAe,CAAC,4BAA4B,CAAC,aAAa,CAAC,CAChE,QAAQ,CACT,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;YACtB,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACK,mBAAmB,CACzB,OAA2B,EAC3B,IAAU,EACV,SAAiB,EACjB,mBAA+B,EAC/B,sBAAkC;QAElC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC,WAAW,EAAE;YAChC,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;SACtC;QAED;;WAEG;QACH,IAAI,gBAAgB,GAAG,KAAK,CAAC;QAE7B;;;;WAIG;QACH,OAAO,CAAC,eAAe,CACrB,UAAU,EACV,CAAC,QAAsD,EAAE,EAAE;YACzD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;YAClD,IAAI,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;gBAC1C,QAAQ,CAAC,MAAM,EAAE,CAAC;aACnB;YACD,MAAM,kBAAkB,GAAG,IAAA,8CAAsC,EAC/D,QAAQ,EACR,IAAI,CAAC,iBAAiB,CACvB,CAAC;YACF,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;YACvC,mBAAmB,GAAG,MAAM,CAAC,MAAM,CACjC,mBAAmB,EACnB,IAAA,oDAA4C,EAAC,kBAAkB,CAAC,CACjE,CAAC;YACF,sBAAsB,GAAG,MAAM,CAAC,MAAM,CACpC,sBAAsB,EACtB,IAAA,0DAAkD,EAAC,kBAAkB,CAAC,CACvE,CAAC;YAEF,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC,YAAY,EAAE;gBACjC,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;aACxC;YAED,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAC9D,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAC1B,CAAC;YACF,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,sBAAsB,CAC/C,IAAI,EACJ,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CACnC,CAAC;YAEF,aAAO,CAAC,IAAI,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;YAEzC,MAAM,UAAU,GAAG,GAAG,EAAE;gBACtB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;gBAC7C,IAAI,gBAAgB,EAAE;oBACpB,OAAO;iBACR;gBACD,gBAAgB,GAAG,IAAI,CAAC;gBACxB,IAAI,MAAkB,CAAC;gBAEvB,IAAI,QAAQ,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;oBAC1C,MAAM,GAAG,EAAE,IAAI,EAAE,oBAAc,CAAC,KAAK,EAAE,CAAC;iBACzC;qBAAM;oBACL,uCAAuC;oBACvC,MAAM,GAAG;wBACP,IAAI,EAAE,IAAA,2BAAmB,EAAC,cAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,UAAU,CAAC;qBAChE,CAAC;iBACH;gBAED,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;gBAEvB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC,2BAA2B,EAAE;oBAChD,IAAA,wCAAsB,EACpB,GAAG,EAAE,CACH,IAAI,CAAC,SAAS,EAAE,CAAC,2BAA4B,CAC3C,IAAI,EACJ,OAAO,EACP,QAAQ,CACT,EACH,GAAG,EAAE,GAAE,CAAC,EACR,IAAI,CACL,CAAC;iBACH;gBAED,IAAI,CAAC,cAAc,CACjB,IAAI,EACJ,cAAQ,CAAC,MAAM,EACf,SAAS,EACT,mBAAmB,EACnB,sBAAsB,CACvB,CAAC;YACJ,CAAC,CAAC;YAEF,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;YAC/B,QAAQ,CAAC,EAAE,CAAC,qBAAY,EAAE,CAAC,KAAU,EAAE,EAAE;gBACvC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;gBACtD,IAAI,gBAAgB,EAAE;oBACpB,OAAO;iBACR;gBACD,gBAAgB,GAAG,IAAI,CAAC;gBACxB,IAAA,wBAAgB,EAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBACtD,IAAI,CAAC,SAAS,CAAC;oBACb,IAAI,EAAE,oBAAc,CAAC,KAAK;oBAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;iBACvB,CAAC,CAAC;gBACH,IAAI,CAAC,cAAc,CACjB,IAAI,EACJ,cAAQ,CAAC,MAAM,EACf,SAAS,EACT,mBAAmB,EACnB,sBAAsB,CACvB,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,CACF,CAAC;QACF,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YACvB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;YACvD,IAAI,OAAO,CAAC,OAAO,IAAI,gBAAgB,EAAE;gBACvC,OAAO;aACR;YACD,gBAAgB,GAAG,IAAI,CAAC;YACxB,IAAI,CAAC,cAAc,CACjB,IAAI,EACJ,cAAQ,CAAC,MAAM,EACf,SAAS,EACT,mBAAmB,EACnB,sBAAsB,CACvB,CAAC;QACJ,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,qBAAY,EAAE,CAAC,KAAU,EAAE,EAAE;YACtC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC9D,IAAI,gBAAgB,EAAE;gBACpB,OAAO;aACR;YACD,gBAAgB,GAAG,IAAI,CAAC;YACxB,IAAA,wBAAgB,EAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAEtD,IAAI,CAAC,cAAc,CACjB,IAAI,EACJ,cAAQ,CAAC,MAAM,EACf,SAAS,EACT,mBAAmB,EACnB,sBAAsB,CACvB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACtD,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,wBAAwB,CAC9B,SAA2B,EAC3B,QAAwD;QAExD,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,SAAS,eAAe,CAE7B,KAAa,EACb,GAAG,IAAe;YAElB,6BAA6B;YAC7B,IAAI,KAAK,KAAK,SAAS,EAAE;gBACvB,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;aAC/C;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAyB,CAAC;YAChD,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,CAA6C,CAAC;YACrE,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC;YAEvC,eAAe,CAAC,KAAK,CAAC,KAAK,CACzB,GAAG,SAAS,kCAAkC,CAC/C,CAAC;YAEF,IACE,IAAA,wCAAsB,EACpB,GAAG,EAAE,CACH,eAAe,CAAC,SAAS,EAAE,CAAC,yBAAyB,EAAE,CAAC,OAAO,CAAC,EAClE,CAAC,CAAU,EAAE,EAAE;gBACb,IAAI,CAAC,IAAI,IAAI,EAAE;oBACb,eAAe,CAAC,KAAK,CAAC,KAAK,CACzB,0CAA0C,EAC1C,CAAC,CACF,CAAC;iBACH;YACH,CAAC,EACD,IAAI,CACL,EACD;gBACA,OAAO,aAAO,CAAC,IAAI,CAAC,IAAA,sBAAe,EAAC,aAAO,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE;oBAC1D,aAAO,CAAC,IAAI,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,OAAO,CAAC,CAAC;oBACxC,aAAO,CAAC,IAAI,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;oBACzC,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;gBAChD,CAAC,CAAC,CAAC;aACJ;YAED,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;YAEhC,MAAM,cAAc,GAAG,IAAA,oCAA4B,EACjD,OAAO,EACP;gBACE,SAAS,EAAE,SAAS;gBACpB,UAAU,EAAE,eAAe,CAAC,SAAS,EAAE,CAAC,UAAU;gBAClD,cAAc,EAAE,eAAe,CAAC,kBAAkB,CAChD,OAAO,EACP,eAAe,CAAC,SAAS,EAAE,CAAC,qBAAqB,CAClD;gBACD,gBAAgB,EAAE,eAAe,CAAC,iBAAiB;gBACnD,8BAA8B,EAC5B,eAAe,CAAC,SAAS,EAAE,CAAC,8BAA8B,IAAI,KAAK;aACtE,EACD,eAAe,CAAC,KAAK,CACtB,CAAC;YAEF,MAAM,WAAW,GAAgB;gBAC/B,IAAI,EAAE,cAAQ,CAAC,MAAM;gBACrB,UAAU,EAAE,cAAc;aAC3B,CAAC;YAEF,MAAM,SAAS,GAAG,IAAA,aAAM,GAAE,CAAC;YAC3B,MAAM,mBAAmB,GACvB,IAAA,0CAAkC,EAAC,cAAc,CAAC,CAAC;YAErD,kEAAkE;YAClE,MAAM,sBAAsB,GAAe;gBACzC,CAAC,+CAAwB,CAAC,EAAE,cAAc,CAAC,+CAAwB,CAAC;gBACpE,CAAC,sCAAe,CAAC,EAAE,cAAc,CAAC,sCAAe,CAAC;aACnD,CAAC;YAEF,uEAAuE;YACvE,IAAI,cAAc,CAAC,oDAA6B,CAAC,EAAE;gBACjD,sBAAsB,CAAC,oDAA6B,CAAC;oBACnD,cAAc,CAAC,oDAA6B,CAAC,CAAC;aACjD;YAED,MAAM,GAAG,GAAG,iBAAW,CAAC,OAAO,CAAC,kBAAY,EAAE,OAAO,CAAC,CAAC;YACvD,MAAM,IAAI,GAAG,eAAe,CAAC,cAAc,CAAC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC;YACtE,MAAM,WAAW,GAAgB;gBAC/B,IAAI,EAAE,cAAO,CAAC,IAAI;gBAClB,IAAI;aACL,CAAC;YAEF,OAAO,aAAO,CAAC,IAAI,CACjB,IAAA,qBAAc,EAAC,WAAK,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,WAAW,CAAC,EACrD,GAAG,EAAE;gBACH,aAAO,CAAC,IAAI,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,OAAO,CAAC,CAAC;gBACxC,aAAO,CAAC,IAAI,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;gBAEzC,IAAI,eAAe,CAAC,SAAS,EAAE,CAAC,WAAW,EAAE;oBAC3C,eAAe,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;iBACjD;gBACD,IAAI,eAAe,CAAC,SAAS,EAAE,CAAC,YAAY,EAAE;oBAC5C,eAAe,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;iBACnD;gBAED,eAAe,CAAC,cAAc,CAAC,MAAM,CAAC,qBAAqB,CACzD,IAAI,EACJ,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAClC,CAAC;gBAEF,yEAAyE;gBACzE,IAAI,QAAQ,GAAG,KAAK,CAAC;gBACrB,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;oBACxB,IAAI,QAAQ,EAAE;wBACZ,OAAO;qBACR;oBACD,eAAe,CAAC,uBAAuB,CACrC,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,mBAAmB,EACnB,sBAAsB,EACtB,SAAS,CACV,CAAC;gBACJ,CAAC,CAAC,CAAC;gBACH,QAAQ,CAAC,EAAE,CAAC,qBAAY,EAAE,CAAC,GAAQ,EAAE,EAAE;oBACrC,QAAQ,GAAG,IAAI,CAAC;oBAChB,eAAe,CAAC,sBAAsB,CACpC,IAAI,EACJ,mBAAmB,EACnB,sBAAsB,EACtB,SAAS,EACT,GAAG,CACJ,CAAC;gBACJ,CAAC,CAAC,CAAC;gBAEH,OAAO,IAAA,wCAAsB,EAC3B,GAAG,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC,EAC5C,KAAK,CAAC,EAAE;oBACN,IAAI,KAAK,EAAE;wBACT,IAAA,wBAAgB,EACd,IAAI,EACJ,KAAK,EACL,eAAe,CAAC,iBAAiB,CAClC,CAAC;wBACF,eAAe,CAAC,cAAc,CAC5B,IAAI,EACJ,cAAQ,CAAC,MAAM,EACf,SAAS,EACT,mBAAmB,EACnB,sBAAsB,CACvB,CAAC;wBACF,MAAM,KAAK,CAAC;qBACb;gBACH,CAAC,CACF,CAAC;YACJ,CAAC,CACF,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,wBAAwB,CAC9B,SAA2B,EAC3B,QAAkC;QAElC,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,SAAS,eAAe,CAE7B,OAA+C,EAC/C,GAAG,IAAe;YAElB,IAAI,CAAC,IAAA,0BAAkB,EAAC,OAAO,CAAC,EAAE;gBAChC,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;aACjD;YACD,MAAM,YAAY,GAChB,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ;gBAC3B,CAAC,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,YAAY,GAAG,CAAC,GAAG,CAAC;gBACzD,CAAC,CAAE,IAAI,CAAC,KAAK,EAA0B;gBACvC,CAAC,CAAC,SAAS,CAAC;YAChB,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,IAAA,sBAAc,EAC1D,eAAe,CAAC,KAAK,EACrB,OAAO,EACP,YAAY,CACb,CAAC;YAEF,IACE,IAAA,wCAAsB,EACpB,GAAG,EAAE,CACH,eAAe;iBACZ,SAAS,EAAE;iBACX,yBAAyB,EAAE,CAAC,aAAa,CAAC,EAC/C,CAAC,CAAU,EAAE,EAAE;gBACb,IAAI,CAAC,IAAI,IAAI,EAAE;oBACb,eAAe,CAAC,KAAK,CAAC,KAAK,CACzB,0CAA0C,EAC1C,CAAC,CACF,CAAC;iBACH;YACH,CAAC,EACD,IAAI,CACL,EACD;gBACA,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;aACvD;YAED,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,IAAA,8BAAsB,EAAC,aAAa,CAAC,CAAC;YACjE,MAAM,UAAU,GAAG,IAAA,oCAA4B,EAC7C,aAAa,EACb;gBACE,SAAS;gBACT,IAAI;gBACJ,QAAQ;gBACR,cAAc,EAAE,eAAe,CAAC,kBAAkB,CAChD,aAAa,EACb,eAAe,CAAC,SAAS,EAAE,CAAC,qBAAqB,CAClD;aACF,EACD,eAAe,CAAC,iBAAiB,EACjC,eAAe,CAAC,SAAS,EAAE,CAAC,8BAA8B,IAAI,KAAK,CACpE,CAAC;YAEF,MAAM,SAAS,GAAG,IAAA,aAAM,GAAE,CAAC;YAC3B,MAAM,mBAAmB,GACvB,IAAA,0CAAkC,EAAC,UAAU,CAAC,CAAC;YAEjD,oFAAoF;YACpF,MAAM,sBAAsB,GAAe;gBACzC,CAAC,+CAAwB,CAAC,EAAE,UAAU,CAAC,+CAAwB,CAAC;gBAChE,CAAC,0CAAmB,CAAC,EAAE,UAAU,CAAC,0CAAmB,CAAC;gBACtD,CAAC,uCAAgB,CAAC,EAAE,UAAU,CAAC,uCAAgB,CAAC;aACjD,CAAC;YAEF,iEAAiE;YACjE,IAAI,UAAU,CAAC,qDAA8B,CAAC,EAAE;gBAC9C,sBAAsB,CAAC,qDAA8B,CAAC;oBACpD,UAAU,CAAC,qDAA8B,CAAC,CAAC;aAC9C;YAED,uEAAuE;YACvE,IAAI,UAAU,CAAC,oDAA6B,CAAC,EAAE;gBAC7C,sBAAsB,CAAC,oDAA6B,CAAC;oBACnD,UAAU,CAAC,oDAA6B,CAAC,CAAC;aAC7C;YAED,MAAM,WAAW,GAAgB;gBAC/B,IAAI,EAAE,cAAQ,CAAC,MAAM;gBACrB,UAAU;aACX,CAAC;YACF,MAAM,IAAI,GAAG,eAAe,CAAC,cAAc,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;YAEjE,MAAM,aAAa,GAAG,aAAO,CAAC,MAAM,EAAE,CAAC;YACvC,MAAM,cAAc,GAAG,WAAK,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;YAE1D,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;gBAC1B,aAAa,CAAC,OAAO,GAAG,EAAE,CAAC;aAC5B;iBAAM;gBACL,oEAAoE;gBACpE,oCAAoC;gBACpC,aAAa,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC;aAClE;YACD,iBAAW,CAAC,MAAM,CAAC,cAAc,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC;YAE1D,OAAO,aAAO,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,EAAE;gBACvC;;;mBAGG;gBACH,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACjC,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE;oBAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,aAAO,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;iBACzD;gBAED,MAAM,OAAO,GAAuB,IAAA,wCAAsB,EACxD,GAAG,EAAE;oBACH,IAAI,UAAU,EAAE;wBACd,qGAAqG;wBACrG,qGAAqG;wBACrG,uBAAuB;wBACvB,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;qBACjD;yBAAM;wBACL,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;qBACvD;gBACH,CAAC,EACD,KAAK,CAAC,EAAE;oBACN,IAAI,KAAK,EAAE;wBACT,IAAA,wBAAgB,EAAC,IAAI,EAAE,KAAK,EAAE,eAAe,CAAC,iBAAiB,CAAC,CAAC;wBAEjE,eAAe,CAAC,cAAc,CAC5B,IAAI,EACJ,cAAQ,CAAC,MAAM,EACf,SAAS,EACT,mBAAmB,EACnB,sBAAsB,CACvB,CAAC;wBACF,MAAM,KAAK,CAAC;qBACb;gBACH,CAAC,CACF,CAAC;gBAEF,eAAe,CAAC,KAAK,CAAC,KAAK,CACzB,GAAG,SAAS,kCAAkC,CAC/C,CAAC;gBACF,aAAO,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;gBACrC,OAAO,eAAe,CAAC,mBAAmB,CACxC,OAAO,EACP,IAAI,EACJ,SAAS,EACT,mBAAmB,EACnB,sBAAsB,CACvB,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;IACJ,CAAC;IAEO,uBAAuB,CAC7B,OAA6B,EAC7B,QAA6B,EAC7B,IAAU,EACV,mBAA+B,EAC/B,sBAAkC,EAClC,SAAiB;QAEjB,MAAM,UAAU,GAAG,IAAA,8CAAsC,EACvD,OAAO,EACP,QAAQ,EACR,IAAI,CAAC,iBAAiB,CACvB,CAAC;QACF,mBAAmB,GAAG,MAAM,CAAC,MAAM,CACjC,mBAAmB,EACnB,IAAA,oDAA4C,EAAC,UAAU,CAAC,CACzD,CAAC;QACF,sBAAsB,GAAG,MAAM,CAAC,MAAM,CACpC,sBAAsB,EACtB,IAAA,0DAAkD,EAAC,UAAU,CAAC,CAC/D,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAC/D,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAC3B,CAAC;QAEF,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC;YACvC,IAAI,EAAE,IAAA,2BAAmB,EAAC,cAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,UAAU,CAAC;SAChE,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,UAAU,CAAC,sCAAe,CAAC,CAAC;QAC1C,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,MAAM,IAAI,KAAK,IAAI,KAAK,EAAE,CAAC,CAAC;SACxD;QAED,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC,2BAA2B,EAAE;YAChD,IAAA,wCAAsB,EACpB,GAAG,EAAE,CACH,IAAI,CAAC,SAAS,EAAE,CAAC,2BAA4B,CAC3C,IAAI,EACJ,OAAO,EACP,QAAQ,CACT,EACH,GAAG,EAAE,GAAE,CAAC,EACR,IAAI,CACL,CAAC;SACH;QAED,IAAI,CAAC,cAAc,CACjB,IAAI,EACJ,cAAQ,CAAC,MAAM,EACf,SAAS,EACT,mBAAmB,EACnB,sBAAsB,CACvB,CAAC;IACJ,CAAC;IAEO,sBAAsB,CAC5B,IAAU,EACV,mBAA+B,EAC/B,sBAAkC,EAClC,SAAiB,EACjB,KAAU;QAEV,IAAA,wBAAgB,EAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACtD,wCAAwC;QACxC,IAAI,CAAC,cAAc,CACjB,IAAI,EACJ,cAAQ,CAAC,MAAM,EACf,SAAS,EACT,mBAAmB,EACnB,sBAAsB,CACvB,CAAC;IACJ,CAAC;IAEO,cAAc,CACpB,IAAY,EACZ,OAAoB,EACpB,GAAG,GAAG,aAAO,CAAC,MAAM,EAAE;QAEtB;;;WAGG;QACH,MAAM,aAAa,GACjB,OAAO,CAAC,IAAI,KAAK,cAAQ,CAAC,MAAM;YAC9B,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,6BAA6B;YAChD,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,6BAA6B,CAAC;QAErD,IAAI,IAAU,CAAC;QACf,MAAM,WAAW,GAAG,WAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAEvC,IAAI,aAAa,KAAK,IAAI,IAAI,WAAW,KAAK,SAAS,EAAE;YACvD,IAAI,GAAG,WAAK,CAAC,eAAe,CAAC,0BAAoB,CAAC,CAAC;SACpD;aAAM,IAAI,aAAa,KAAK,IAAI,IAAI,WAAW,EAAE,WAAW,EAAE,CAAC,QAAQ,EAAE;YACxE,IAAI,GAAG,WAAW,CAAC;SACpB;aAAM;YACL,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;SAClD;QACD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,cAAc,CACpB,IAAU,EACV,QAAkB,EAClB,SAAiB,EACjB,mBAA+B,EAC/B,sBAAkC;QAElC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACjC,OAAO;SACR;QAED,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEhC,iBAAiB;QACjB,MAAM,QAAQ,GAAG,IAAA,2BAAoB,EAAC,IAAA,qBAAc,EAAC,SAAS,EAAE,IAAA,aAAM,GAAE,CAAC,CAAC,CAAC;QAC3E,IAAI,QAAQ,KAAK,cAAQ,CAAC,MAAM,EAAE;YAChC,IAAI,CAAC,qBAAqB,CACxB,QAAQ,EACR,mBAAmB,EACnB,sBAAsB,CACvB,CAAC;SACH;aAAM,IAAI,QAAQ,KAAK,cAAQ,CAAC,MAAM,EAAE;YACvC,IAAI,CAAC,qBAAqB,CACxB,QAAQ,EACR,mBAAmB,EACnB,sBAAsB,CACvB,CAAC;SACH;IACH,CAAC;IAEO,iBAAiB,CACvB,IAAU,EACV,QAAoD;QAEpD,IAAA,wCAAsB,EACpB,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,YAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,EACpD,GAAG,EAAE,GAAE,CAAC,EACR,IAAI,CACL,CAAC;IACJ,CAAC;IAEO,gBAAgB,CACtB,IAAU,EACV,OAAkD;QAElD,IAAA,wCAAsB,EACpB,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,WAAY,CAAC,IAAI,EAAE,OAAO,CAAC,EAClD,GAAG,EAAE,GAAE,CAAC,EACR,IAAI,CACL,CAAC;IACJ,CAAC;IAEO,kBAAkB,CACxB,OAAmD,EACnD,QAA8B;QAE9B,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;YAClC,OAAO,IAAA,wCAAsB,EAC3B,GAAG,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,EACvB,GAAG,EAAE,GAAE,CAAC,EACR,IAAI,CACL,CAAC;SACH;IACH,CAAC;IAEO,oBAAoB;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAEhC,OAAO;YACL,MAAM,EAAE;gBACN,qBAAqB,EAAE,IAAA,qBAAa,EAClC,SAAS,EACT,MAAM,CAAC,uBAAuB,EAAE,MAAM,EAAE,cAAc,IAAI,EAAE,CAC7D;gBACD,sBAAsB,EAAE,IAAA,qBAAa,EACnC,UAAU,EACV,MAAM,CAAC,uBAAuB,EAAE,MAAM,EAAE,eAAe,IAAI,EAAE,CAC9D;aACF;YACD,MAAM,EAAE;gBACN,qBAAqB,EAAE,IAAA,qBAAa,EAClC,SAAS,EACT,MAAM,CAAC,uBAAuB,EAAE,MAAM,EAAE,cAAc,IAAI,EAAE,CAC7D;gBACD,sBAAsB,EAAE,IAAA,qBAAa,EACnC,UAAU,EACV,MAAM,CAAC,uBAAuB,EAAE,MAAM,EAAE,eAAe,IAAI,EAAE,CAC9D;aACF;SACF,CAAC;IACJ,CAAC;CACF;AAt9BD,kDAs9BC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  context,\n  HrTime,\n  INVALID_SPAN_CONTEXT,\n  propagation,\n  ROOT_CONTEXT,\n  Span,\n  SpanKind,\n  SpanOptions,\n  SpanStatus,\n  SpanStatusCode,\n  trace,\n  Histogram,\n  Attributes,\n  ValueType,\n} from '@opentelemetry/api';\nimport {\n  hrTime,\n  hrTimeDuration,\n  hrTimeToMilliseconds,\n  suppressTracing,\n  RPCMetadata,\n  RPCType,\n  setRPCMetadata,\n} from '@opentelemetry/core';\nimport type * as http from 'http';\nimport type * as https from 'https';\nimport { Socket } from 'net';\nimport * as url from 'url';\nimport { HttpInstrumentationConfig } from './types';\nimport { VERSION } from './version';\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  SemconvStability,\n  semconvStabilityFromStr,\n  safeExecuteInTheMiddle,\n} from '@opentelemetry/instrumentation';\nimport { errorMonitor } from 'events';\nimport {\n  ATTR_HTTP_REQUEST_METHOD,\n  ATTR_HTTP_RESPONSE_STATUS_CODE,\n  ATTR_NETWORK_PROTOCOL_VERSION,\n  ATTR_HTTP_ROUTE,\n  ATTR_SERVER_ADDRESS,\n  ATTR_SERVER_PORT,\n  ATTR_URL_SCHEME,\n  METRIC_HTTP_CLIENT_REQUEST_DURATION,\n  METRIC_HTTP_SERVER_REQUEST_DURATION,\n} from '@opentelemetry/semantic-conventions';\nimport {\n  extractHostnameAndPort,\n  getIncomingRequestAttributes,\n  getIncomingRequestAttributesOnResponse,\n  getIncomingRequestMetricAttributes,\n  getIncomingRequestMetricAttributesOnResponse,\n  getIncomingStableRequestMetricAttributesOnResponse,\n  getOutgoingRequestAttributes,\n  getOutgoingRequestAttributesOnResponse,\n  getOutgoingRequestMetricAttributes,\n  getOutgoingRequestMetricAttributesOnResponse,\n  getOutgoingStableRequestMetricAttributesOnResponse,\n  getRequestInfo,\n  headerCapture,\n  isValidOptionsType,\n  parseResponseStatus,\n  setSpanWithError,\n} from './utils';\nimport { Err, Func, Http, HttpRequestArgs, Https } from './internal-types';\n\n/**\n * `node:http` and `node:https` instrumentation for OpenTelemetry\n */\nexport class HttpInstrumentation extends InstrumentationBase<HttpInstrumentationConfig> {\n  /** keep track on spans not ended */\n  private readonly _spanNotEnded: WeakSet<Span> = new WeakSet<Span>();\n  private _headerCapture;\n  declare private _oldHttpServerDurationHistogram: Histogram;\n  declare private _stableHttpServerDurationHistogram: Histogram;\n  declare private _oldHttpClientDurationHistogram: Histogram;\n  declare private _stableHttpClientDurationHistogram: Histogram;\n\n  private _semconvStability: SemconvStability = SemconvStability.OLD;\n\n  constructor(config: HttpInstrumentationConfig = {}) {\n    super('@opentelemetry/instrumentation-http', VERSION, config);\n    this._headerCapture = this._createHeaderCapture();\n    this._semconvStability = semconvStabilityFromStr(\n      'http',\n      process.env.OTEL_SEMCONV_STABILITY_OPT_IN\n    );\n  }\n\n  protected override _updateMetricInstruments() {\n    this._oldHttpServerDurationHistogram = this.meter.createHistogram(\n      'http.server.duration',\n      {\n        description: 'Measures the duration of inbound HTTP requests.',\n        unit: 'ms',\n        valueType: ValueType.DOUBLE,\n      }\n    );\n    this._oldHttpClientDurationHistogram = this.meter.createHistogram(\n      'http.client.duration',\n      {\n        description: 'Measures the duration of outbound HTTP requests.',\n        unit: 'ms',\n        valueType: ValueType.DOUBLE,\n      }\n    );\n    this._stableHttpServerDurationHistogram = this.meter.createHistogram(\n      METRIC_HTTP_SERVER_REQUEST_DURATION,\n      {\n        description: 'Duration of HTTP server requests.',\n        unit: 's',\n        valueType: ValueType.DOUBLE,\n        advice: {\n          explicitBucketBoundaries: [\n            0.005, 0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1, 2.5, 5,\n            7.5, 10,\n          ],\n        },\n      }\n    );\n    this._stableHttpClientDurationHistogram = this.meter.createHistogram(\n      METRIC_HTTP_CLIENT_REQUEST_DURATION,\n      {\n        description: 'Duration of HTTP client requests.',\n        unit: 's',\n        valueType: ValueType.DOUBLE,\n        advice: {\n          explicitBucketBoundaries: [\n            0.005, 0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1, 2.5, 5,\n            7.5, 10,\n          ],\n        },\n      }\n    );\n  }\n\n  private _recordServerDuration(\n    durationMs: number,\n    oldAttributes: Attributes,\n    stableAttributes: Attributes\n  ) {\n    if (this._semconvStability & SemconvStability.OLD) {\n      // old histogram is counted in MS\n      this._oldHttpServerDurationHistogram.record(durationMs, oldAttributes);\n    }\n\n    if (this._semconvStability & SemconvStability.STABLE) {\n      // stable histogram is counted in S\n      this._stableHttpServerDurationHistogram.record(\n        durationMs / 1000,\n        stableAttributes\n      );\n    }\n  }\n\n  private _recordClientDuration(\n    durationMs: number,\n    oldAttributes: Attributes,\n    stableAttributes: Attributes\n  ) {\n    if (this._semconvStability & SemconvStability.OLD) {\n      // old histogram is counted in MS\n      this._oldHttpClientDurationHistogram.record(durationMs, oldAttributes);\n    }\n\n    if (this._semconvStability & SemconvStability.STABLE) {\n      // stable histogram is counted in S\n      this._stableHttpClientDurationHistogram.record(\n        durationMs / 1000,\n        stableAttributes\n      );\n    }\n  }\n\n  override setConfig(config: HttpInstrumentationConfig = {}): void {\n    super.setConfig(config);\n    this._headerCapture = this._createHeaderCapture();\n  }\n\n  init(): [\n    InstrumentationNodeModuleDefinition,\n    InstrumentationNodeModuleDefinition,\n  ] {\n    return [this._getHttpsInstrumentation(), this._getHttpInstrumentation()];\n  }\n\n  private _getHttpInstrumentation() {\n    return new InstrumentationNodeModuleDefinition(\n      'http',\n      ['*'],\n      (moduleExports: Http): Http => {\n        const isESM = (moduleExports as any)[Symbol.toStringTag] === 'Module';\n        if (!this.getConfig().disableOutgoingRequestInstrumentation) {\n          const patchedRequest = this._wrap(\n            moduleExports,\n            'request',\n            this._getPatchOutgoingRequestFunction('http')\n          ) as unknown as Func<http.ClientRequest>;\n          const patchedGet = this._wrap(\n            moduleExports,\n            'get',\n            this._getPatchOutgoingGetFunction(patchedRequest)\n          );\n          if (isESM) {\n            // To handle `import http from 'http'`, which returns the default\n            // export, we need to set `module.default.*`.\n            (moduleExports as any).default.request = patchedRequest;\n            (moduleExports as any).default.get = patchedGet;\n          }\n        }\n        if (!this.getConfig().disableIncomingRequestInstrumentation) {\n          this._wrap(\n            moduleExports.Server.prototype,\n            'emit',\n            this._getPatchIncomingRequestFunction('http')\n          );\n        }\n        return moduleExports;\n      },\n      (moduleExports: Http) => {\n        if (moduleExports === undefined) return;\n\n        if (!this.getConfig().disableOutgoingRequestInstrumentation) {\n          this._unwrap(moduleExports, 'request');\n          this._unwrap(moduleExports, 'get');\n        }\n        if (!this.getConfig().disableIncomingRequestInstrumentation) {\n          this._unwrap(moduleExports.Server.prototype, 'emit');\n        }\n      }\n    );\n  }\n\n  private _getHttpsInstrumentation() {\n    return new InstrumentationNodeModuleDefinition(\n      'https',\n      ['*'],\n      (moduleExports: Https): Https => {\n        const isESM = (moduleExports as any)[Symbol.toStringTag] === 'Module';\n        if (!this.getConfig().disableOutgoingRequestInstrumentation) {\n          const patchedRequest = this._wrap(\n            moduleExports,\n            'request',\n            this._getPatchHttpsOutgoingRequestFunction('https')\n          ) as unknown as Func<http.ClientRequest>;\n          const patchedGet = this._wrap(\n            moduleExports,\n            'get',\n            this._getPatchHttpsOutgoingGetFunction(patchedRequest)\n          );\n          if (isESM) {\n            // To handle `import https from 'https'`, which returns the default\n            // export, we need to set `module.default.*`.\n            (moduleExports as any).default.request = patchedRequest;\n            (moduleExports as any).default.get = patchedGet;\n          }\n        }\n        if (!this.getConfig().disableIncomingRequestInstrumentation) {\n          this._wrap(\n            moduleExports.Server.prototype,\n            'emit',\n            this._getPatchIncomingRequestFunction('https')\n          );\n        }\n        return moduleExports;\n      },\n      (moduleExports: Https) => {\n        if (moduleExports === undefined) return;\n\n        if (!this.getConfig().disableOutgoingRequestInstrumentation) {\n          this._unwrap(moduleExports, 'request');\n          this._unwrap(moduleExports, 'get');\n        }\n        if (!this.getConfig().disableIncomingRequestInstrumentation) {\n          this._unwrap(moduleExports.Server.prototype, 'emit');\n        }\n      }\n    );\n  }\n\n  /**\n   * Creates spans for incoming requests, restoring spans' context if applied.\n   */\n  private _getPatchIncomingRequestFunction(component: 'http' | 'https') {\n    return (\n      original: (event: string, ...args: unknown[]) => boolean\n    ): ((this: unknown, event: string, ...args: unknown[]) => boolean) => {\n      return this._incomingRequestFunction(component, original);\n    };\n  }\n\n  /**\n   * Creates spans for outgoing requests, sending spans' context for distributed\n   * tracing.\n   */\n  private _getPatchOutgoingRequestFunction(component: 'http' | 'https') {\n    return (original: Func<http.ClientRequest>): Func<http.ClientRequest> => {\n      return this._outgoingRequestFunction(component, original);\n    };\n  }\n\n  private _getPatchOutgoingGetFunction(\n    clientRequest: (\n      options: http.RequestOptions | string | url.URL,\n      ...args: HttpRequestArgs\n    ) => http.ClientRequest\n  ) {\n    return (_original: Func<http.ClientRequest>): Func<http.ClientRequest> => {\n      // Re-implement http.get. This needs to be done (instead of using\n      // getPatchOutgoingRequestFunction to patch it) because we need to\n      // set the trace context header before the returned http.ClientRequest is\n      // ended. The Node.js docs state that the only differences between\n      // request and get are that (1) get defaults to the HTTP GET method and\n      // (2) the returned request object is ended immediately. The former is\n      // already true (at least in supported Node versions up to v10), so we\n      // simply follow the latter. Ref:\n      // https://nodejs.org/dist/latest/docs/api/http.html#http_http_get_options_callback\n      // https://github.com/googleapis/cloud-trace-nodejs/blob/master/src/instrumentations/instrumentation-http.ts#L198\n      return function outgoingGetRequest<\n        T extends http.RequestOptions | string | url.URL,\n      >(options: T, ...args: HttpRequestArgs): http.ClientRequest {\n        const req = clientRequest(options, ...args);\n        req.end();\n        return req;\n      };\n    };\n  }\n\n  /** Patches HTTPS outgoing requests */\n  private _getPatchHttpsOutgoingRequestFunction(component: 'http' | 'https') {\n    return (original: Func<http.ClientRequest>): Func<http.ClientRequest> => {\n      const instrumentation = this;\n      return function httpsOutgoingRequest(\n        // eslint-disable-next-line node/no-unsupported-features/node-builtins\n        options: https.RequestOptions | string | URL,\n        ...args: HttpRequestArgs\n      ): http.ClientRequest {\n        // Makes sure options will have default HTTPS parameters\n        if (\n          component === 'https' &&\n          typeof options === 'object' &&\n          options?.constructor?.name !== 'URL'\n        ) {\n          options = Object.assign({}, options);\n          instrumentation._setDefaultOptions(options);\n        }\n        return instrumentation._getPatchOutgoingRequestFunction(component)(\n          original\n        )(options, ...args);\n      };\n    };\n  }\n\n  private _setDefaultOptions(options: https.RequestOptions) {\n    options.protocol = options.protocol || 'https:';\n    options.port = options.port || 443;\n  }\n\n  /** Patches HTTPS outgoing get requests */\n  private _getPatchHttpsOutgoingGetFunction(\n    clientRequest: (\n      // eslint-disable-next-line node/no-unsupported-features/node-builtins\n      options: http.RequestOptions | string | URL,\n      ...args: HttpRequestArgs\n    ) => http.ClientRequest\n  ) {\n    return (original: Func<http.ClientRequest>): Func<http.ClientRequest> => {\n      const instrumentation = this;\n      return function httpsOutgoingRequest(\n        // eslint-disable-next-line node/no-unsupported-features/node-builtins\n        options: https.RequestOptions | string | URL,\n        ...args: HttpRequestArgs\n      ): http.ClientRequest {\n        return instrumentation._getPatchOutgoingGetFunction(clientRequest)(\n          original\n        )(options, ...args);\n      };\n    };\n  }\n\n  /**\n   * Attach event listeners to a client request to end span and add span attributes.\n   *\n   * @param request The original request object.\n   * @param span representing the current operation\n   * @param startTime representing the start time of the request to calculate duration in Metric\n   * @param oldMetricAttributes metric attributes for old semantic conventions\n   * @param stableMetricAttributes metric attributes for new semantic conventions\n   */\n  private _traceClientRequest(\n    request: http.ClientRequest,\n    span: Span,\n    startTime: HrTime,\n    oldMetricAttributes: Attributes,\n    stableMetricAttributes: Attributes\n  ): http.ClientRequest {\n    if (this.getConfig().requestHook) {\n      this._callRequestHook(span, request);\n    }\n\n    /**\n     * Determines if the request has errored or the response has ended/errored.\n     */\n    let responseFinished = false;\n\n    /*\n     * User 'response' event listeners can be added before our listener,\n     * force our listener to be the first, so response emitter is bound\n     * before any user listeners are added to it.\n     */\n    request.prependListener(\n      'response',\n      (response: http.IncomingMessage & { aborted?: boolean }) => {\n        this._diag.debug('outgoingRequest on response()');\n        if (request.listenerCount('response') <= 1) {\n          response.resume();\n        }\n        const responseAttributes = getOutgoingRequestAttributesOnResponse(\n          response,\n          this._semconvStability\n        );\n        span.setAttributes(responseAttributes);\n        oldMetricAttributes = Object.assign(\n          oldMetricAttributes,\n          getOutgoingRequestMetricAttributesOnResponse(responseAttributes)\n        );\n        stableMetricAttributes = Object.assign(\n          stableMetricAttributes,\n          getOutgoingStableRequestMetricAttributesOnResponse(responseAttributes)\n        );\n\n        if (this.getConfig().responseHook) {\n          this._callResponseHook(span, response);\n        }\n\n        this._headerCapture.client.captureRequestHeaders(span, header =>\n          request.getHeader(header)\n        );\n        this._headerCapture.client.captureResponseHeaders(\n          span,\n          header => response.headers[header]\n        );\n\n        context.bind(context.active(), response);\n\n        const endHandler = () => {\n          this._diag.debug('outgoingRequest on end()');\n          if (responseFinished) {\n            return;\n          }\n          responseFinished = true;\n          let status: SpanStatus;\n\n          if (response.aborted && !response.complete) {\n            status = { code: SpanStatusCode.ERROR };\n          } else {\n            // behaves same for new and old semconv\n            status = {\n              code: parseResponseStatus(SpanKind.CLIENT, response.statusCode),\n            };\n          }\n\n          span.setStatus(status);\n\n          if (this.getConfig().applyCustomAttributesOnSpan) {\n            safeExecuteInTheMiddle(\n              () =>\n                this.getConfig().applyCustomAttributesOnSpan!(\n                  span,\n                  request,\n                  response\n                ),\n              () => {},\n              true\n            );\n          }\n\n          this._closeHttpSpan(\n            span,\n            SpanKind.CLIENT,\n            startTime,\n            oldMetricAttributes,\n            stableMetricAttributes\n          );\n        };\n\n        response.on('end', endHandler);\n        response.on(errorMonitor, (error: Err) => {\n          this._diag.debug('outgoingRequest on error()', error);\n          if (responseFinished) {\n            return;\n          }\n          responseFinished = true;\n          setSpanWithError(span, error, this._semconvStability);\n          span.setStatus({\n            code: SpanStatusCode.ERROR,\n            message: error.message,\n          });\n          this._closeHttpSpan(\n            span,\n            SpanKind.CLIENT,\n            startTime,\n            oldMetricAttributes,\n            stableMetricAttributes\n          );\n        });\n      }\n    );\n    request.on('close', () => {\n      this._diag.debug('outgoingRequest on request close()');\n      if (request.aborted || responseFinished) {\n        return;\n      }\n      responseFinished = true;\n      this._closeHttpSpan(\n        span,\n        SpanKind.CLIENT,\n        startTime,\n        oldMetricAttributes,\n        stableMetricAttributes\n      );\n    });\n    request.on(errorMonitor, (error: Err) => {\n      this._diag.debug('outgoingRequest on request error()', error);\n      if (responseFinished) {\n        return;\n      }\n      responseFinished = true;\n      setSpanWithError(span, error, this._semconvStability);\n\n      this._closeHttpSpan(\n        span,\n        SpanKind.CLIENT,\n        startTime,\n        oldMetricAttributes,\n        stableMetricAttributes\n      );\n    });\n\n    this._diag.debug('http.ClientRequest return request');\n    return request;\n  }\n\n  private _incomingRequestFunction(\n    component: 'http' | 'https',\n    original: (event: string, ...args: unknown[]) => boolean\n  ) {\n    const instrumentation = this;\n    return function incomingRequest(\n      this: unknown,\n      event: string,\n      ...args: unknown[]\n    ): boolean {\n      // Only traces request events\n      if (event !== 'request') {\n        return original.apply(this, [event, ...args]);\n      }\n\n      const request = args[0] as http.IncomingMessage;\n      const response = args[1] as http.ServerResponse & { socket: Socket };\n      const method = request.method || 'GET';\n\n      instrumentation._diag.debug(\n        `${component} instrumentation incomingRequest`\n      );\n\n      if (\n        safeExecuteInTheMiddle(\n          () =>\n            instrumentation.getConfig().ignoreIncomingRequestHook?.(request),\n          (e: unknown) => {\n            if (e != null) {\n              instrumentation._diag.error(\n                'caught ignoreIncomingRequestHook error: ',\n                e\n              );\n            }\n          },\n          true\n        )\n      ) {\n        return context.with(suppressTracing(context.active()), () => {\n          context.bind(context.active(), request);\n          context.bind(context.active(), response);\n          return original.apply(this, [event, ...args]);\n        });\n      }\n\n      const headers = request.headers;\n\n      const spanAttributes = getIncomingRequestAttributes(\n        request,\n        {\n          component: component,\n          serverName: instrumentation.getConfig().serverName,\n          hookAttributes: instrumentation._callStartSpanHook(\n            request,\n            instrumentation.getConfig().startIncomingSpanHook\n          ),\n          semconvStability: instrumentation._semconvStability,\n          enableSyntheticSourceDetection:\n            instrumentation.getConfig().enableSyntheticSourceDetection || false,\n        },\n        instrumentation._diag\n      );\n\n      const spanOptions: SpanOptions = {\n        kind: SpanKind.SERVER,\n        attributes: spanAttributes,\n      };\n\n      const startTime = hrTime();\n      const oldMetricAttributes =\n        getIncomingRequestMetricAttributes(spanAttributes);\n\n      // request method and url.scheme are both required span attributes\n      const stableMetricAttributes: Attributes = {\n        [ATTR_HTTP_REQUEST_METHOD]: spanAttributes[ATTR_HTTP_REQUEST_METHOD],\n        [ATTR_URL_SCHEME]: spanAttributes[ATTR_URL_SCHEME],\n      };\n\n      // recommended if and only if one was sent, same as span recommendation\n      if (spanAttributes[ATTR_NETWORK_PROTOCOL_VERSION]) {\n        stableMetricAttributes[ATTR_NETWORK_PROTOCOL_VERSION] =\n          spanAttributes[ATTR_NETWORK_PROTOCOL_VERSION];\n      }\n\n      const ctx = propagation.extract(ROOT_CONTEXT, headers);\n      const span = instrumentation._startHttpSpan(method, spanOptions, ctx);\n      const rpcMetadata: RPCMetadata = {\n        type: RPCType.HTTP,\n        span,\n      };\n\n      return context.with(\n        setRPCMetadata(trace.setSpan(ctx, span), rpcMetadata),\n        () => {\n          context.bind(context.active(), request);\n          context.bind(context.active(), response);\n\n          if (instrumentation.getConfig().requestHook) {\n            instrumentation._callRequestHook(span, request);\n          }\n          if (instrumentation.getConfig().responseHook) {\n            instrumentation._callResponseHook(span, response);\n          }\n\n          instrumentation._headerCapture.server.captureRequestHeaders(\n            span,\n            header => request.headers[header]\n          );\n\n          // After 'error', no further events other than 'close' should be emitted.\n          let hasError = false;\n          response.on('close', () => {\n            if (hasError) {\n              return;\n            }\n            instrumentation._onServerResponseFinish(\n              request,\n              response,\n              span,\n              oldMetricAttributes,\n              stableMetricAttributes,\n              startTime\n            );\n          });\n          response.on(errorMonitor, (err: Err) => {\n            hasError = true;\n            instrumentation._onServerResponseError(\n              span,\n              oldMetricAttributes,\n              stableMetricAttributes,\n              startTime,\n              err\n            );\n          });\n\n          return safeExecuteInTheMiddle(\n            () => original.apply(this, [event, ...args]),\n            error => {\n              if (error) {\n                setSpanWithError(\n                  span,\n                  error,\n                  instrumentation._semconvStability\n                );\n                instrumentation._closeHttpSpan(\n                  span,\n                  SpanKind.SERVER,\n                  startTime,\n                  oldMetricAttributes,\n                  stableMetricAttributes\n                );\n                throw error;\n              }\n            }\n          );\n        }\n      );\n    };\n  }\n\n  private _outgoingRequestFunction(\n    component: 'http' | 'https',\n    original: Func<http.ClientRequest>\n  ): Func<http.ClientRequest> {\n    const instrumentation = this;\n    return function outgoingRequest(\n      this: unknown,\n      options: url.URL | http.RequestOptions | string,\n      ...args: unknown[]\n    ): http.ClientRequest {\n      if (!isValidOptionsType(options)) {\n        return original.apply(this, [options, ...args]);\n      }\n      const extraOptions =\n        typeof args[0] === 'object' &&\n        (typeof options === 'string' || options instanceof url.URL)\n          ? (args.shift() as http.RequestOptions)\n          : undefined;\n      const { method, invalidUrl, optionsParsed } = getRequestInfo(\n        instrumentation._diag,\n        options,\n        extraOptions\n      );\n\n      if (\n        safeExecuteInTheMiddle(\n          () =>\n            instrumentation\n              .getConfig()\n              .ignoreOutgoingRequestHook?.(optionsParsed),\n          (e: unknown) => {\n            if (e != null) {\n              instrumentation._diag.error(\n                'caught ignoreOutgoingRequestHook error: ',\n                e\n              );\n            }\n          },\n          true\n        )\n      ) {\n        return original.apply(this, [optionsParsed, ...args]);\n      }\n\n      const { hostname, port } = extractHostnameAndPort(optionsParsed);\n      const attributes = getOutgoingRequestAttributes(\n        optionsParsed,\n        {\n          component,\n          port,\n          hostname,\n          hookAttributes: instrumentation._callStartSpanHook(\n            optionsParsed,\n            instrumentation.getConfig().startOutgoingSpanHook\n          ),\n        },\n        instrumentation._semconvStability,\n        instrumentation.getConfig().enableSyntheticSourceDetection || false\n      );\n\n      const startTime = hrTime();\n      const oldMetricAttributes: Attributes =\n        getOutgoingRequestMetricAttributes(attributes);\n\n      // request method, server address, and server port are both required span attributes\n      const stableMetricAttributes: Attributes = {\n        [ATTR_HTTP_REQUEST_METHOD]: attributes[ATTR_HTTP_REQUEST_METHOD],\n        [ATTR_SERVER_ADDRESS]: attributes[ATTR_SERVER_ADDRESS],\n        [ATTR_SERVER_PORT]: attributes[ATTR_SERVER_PORT],\n      };\n\n      // required if and only if one was sent, same as span requirement\n      if (attributes[ATTR_HTTP_RESPONSE_STATUS_CODE]) {\n        stableMetricAttributes[ATTR_HTTP_RESPONSE_STATUS_CODE] =\n          attributes[ATTR_HTTP_RESPONSE_STATUS_CODE];\n      }\n\n      // recommended if and only if one was sent, same as span recommendation\n      if (attributes[ATTR_NETWORK_PROTOCOL_VERSION]) {\n        stableMetricAttributes[ATTR_NETWORK_PROTOCOL_VERSION] =\n          attributes[ATTR_NETWORK_PROTOCOL_VERSION];\n      }\n\n      const spanOptions: SpanOptions = {\n        kind: SpanKind.CLIENT,\n        attributes,\n      };\n      const span = instrumentation._startHttpSpan(method, spanOptions);\n\n      const parentContext = context.active();\n      const requestContext = trace.setSpan(parentContext, span);\n\n      if (!optionsParsed.headers) {\n        optionsParsed.headers = {};\n      } else {\n        // Make a copy of the headers object to avoid mutating an object the\n        // caller might have a reference to.\n        optionsParsed.headers = Object.assign({}, optionsParsed.headers);\n      }\n      propagation.inject(requestContext, optionsParsed.headers);\n\n      return context.with(requestContext, () => {\n        /*\n         * The response callback is registered before ClientRequest is bound,\n         * thus it is needed to bind it before the function call.\n         */\n        const cb = args[args.length - 1];\n        if (typeof cb === 'function') {\n          args[args.length - 1] = context.bind(parentContext, cb);\n        }\n\n        const request: http.ClientRequest = safeExecuteInTheMiddle(\n          () => {\n            if (invalidUrl) {\n              // we know that the url is invalid, there's no point in injecting context as it will fail validation.\n              // Passing in what the user provided will give the user an error that matches what they'd see without\n              // the instrumentation.\n              return original.apply(this, [options, ...args]);\n            } else {\n              return original.apply(this, [optionsParsed, ...args]);\n            }\n          },\n          error => {\n            if (error) {\n              setSpanWithError(span, error, instrumentation._semconvStability);\n\n              instrumentation._closeHttpSpan(\n                span,\n                SpanKind.CLIENT,\n                startTime,\n                oldMetricAttributes,\n                stableMetricAttributes\n              );\n              throw error;\n            }\n          }\n        );\n\n        instrumentation._diag.debug(\n          `${component} instrumentation outgoingRequest`\n        );\n        context.bind(parentContext, request);\n        return instrumentation._traceClientRequest(\n          request,\n          span,\n          startTime,\n          oldMetricAttributes,\n          stableMetricAttributes\n        );\n      });\n    };\n  }\n\n  private _onServerResponseFinish(\n    request: http.IncomingMessage,\n    response: http.ServerResponse,\n    span: Span,\n    oldMetricAttributes: Attributes,\n    stableMetricAttributes: Attributes,\n    startTime: HrTime\n  ) {\n    const attributes = getIncomingRequestAttributesOnResponse(\n      request,\n      response,\n      this._semconvStability\n    );\n    oldMetricAttributes = Object.assign(\n      oldMetricAttributes,\n      getIncomingRequestMetricAttributesOnResponse(attributes)\n    );\n    stableMetricAttributes = Object.assign(\n      stableMetricAttributes,\n      getIncomingStableRequestMetricAttributesOnResponse(attributes)\n    );\n\n    this._headerCapture.server.captureResponseHeaders(span, header =>\n      response.getHeader(header)\n    );\n\n    span.setAttributes(attributes).setStatus({\n      code: parseResponseStatus(SpanKind.SERVER, response.statusCode),\n    });\n\n    const route = attributes[ATTR_HTTP_ROUTE];\n    if (route) {\n      span.updateName(`${request.method || 'GET'} ${route}`);\n    }\n\n    if (this.getConfig().applyCustomAttributesOnSpan) {\n      safeExecuteInTheMiddle(\n        () =>\n          this.getConfig().applyCustomAttributesOnSpan!(\n            span,\n            request,\n            response\n          ),\n        () => {},\n        true\n      );\n    }\n\n    this._closeHttpSpan(\n      span,\n      SpanKind.SERVER,\n      startTime,\n      oldMetricAttributes,\n      stableMetricAttributes\n    );\n  }\n\n  private _onServerResponseError(\n    span: Span,\n    oldMetricAttributes: Attributes,\n    stableMetricAttributes: Attributes,\n    startTime: HrTime,\n    error: Err\n  ) {\n    setSpanWithError(span, error, this._semconvStability);\n    // TODO get error attributes for metrics\n    this._closeHttpSpan(\n      span,\n      SpanKind.SERVER,\n      startTime,\n      oldMetricAttributes,\n      stableMetricAttributes\n    );\n  }\n\n  private _startHttpSpan(\n    name: string,\n    options: SpanOptions,\n    ctx = context.active()\n  ) {\n    /*\n     * If a parent is required but not present, we use a `NoopSpan` to still\n     * propagate context without recording it.\n     */\n    const requireParent =\n      options.kind === SpanKind.CLIENT\n        ? this.getConfig().requireParentforOutgoingSpans\n        : this.getConfig().requireParentforIncomingSpans;\n\n    let span: Span;\n    const currentSpan = trace.getSpan(ctx);\n\n    if (requireParent === true && currentSpan === undefined) {\n      span = trace.wrapSpanContext(INVALID_SPAN_CONTEXT);\n    } else if (requireParent === true && currentSpan?.spanContext().isRemote) {\n      span = currentSpan;\n    } else {\n      span = this.tracer.startSpan(name, options, ctx);\n    }\n    this._spanNotEnded.add(span);\n    return span;\n  }\n\n  private _closeHttpSpan(\n    span: Span,\n    spanKind: SpanKind,\n    startTime: HrTime,\n    oldMetricAttributes: Attributes,\n    stableMetricAttributes: Attributes\n  ) {\n    if (!this._spanNotEnded.has(span)) {\n      return;\n    }\n\n    span.end();\n    this._spanNotEnded.delete(span);\n\n    // Record metrics\n    const duration = hrTimeToMilliseconds(hrTimeDuration(startTime, hrTime()));\n    if (spanKind === SpanKind.SERVER) {\n      this._recordServerDuration(\n        duration,\n        oldMetricAttributes,\n        stableMetricAttributes\n      );\n    } else if (spanKind === SpanKind.CLIENT) {\n      this._recordClientDuration(\n        duration,\n        oldMetricAttributes,\n        stableMetricAttributes\n      );\n    }\n  }\n\n  private _callResponseHook(\n    span: Span,\n    response: http.IncomingMessage | http.ServerResponse\n  ) {\n    safeExecuteInTheMiddle(\n      () => this.getConfig().responseHook!(span, response),\n      () => {},\n      true\n    );\n  }\n\n  private _callRequestHook(\n    span: Span,\n    request: http.ClientRequest | http.IncomingMessage\n  ) {\n    safeExecuteInTheMiddle(\n      () => this.getConfig().requestHook!(span, request),\n      () => {},\n      true\n    );\n  }\n\n  private _callStartSpanHook(\n    request: http.IncomingMessage | http.RequestOptions,\n    hookFunc: Function | undefined\n  ) {\n    if (typeof hookFunc === 'function') {\n      return safeExecuteInTheMiddle(\n        () => hookFunc(request),\n        () => {},\n        true\n      );\n    }\n  }\n\n  private _createHeaderCapture() {\n    const config = this.getConfig();\n\n    return {\n      client: {\n        captureRequestHeaders: headerCapture(\n          'request',\n          config.headersToSpanAttributes?.client?.requestHeaders ?? []\n        ),\n        captureResponseHeaders: headerCapture(\n          'response',\n          config.headersToSpanAttributes?.client?.responseHeaders ?? []\n        ),\n      },\n      server: {\n        captureRequestHeaders: headerCapture(\n          'request',\n          config.headersToSpanAttributes?.server?.requestHeaders ?? []\n        ),\n        captureResponseHeaders: headerCapture(\n          'response',\n          config.headersToSpanAttributes?.server?.responseHeaders ?? []\n        ),\n      },\n    };\n  }\n}\n"]}