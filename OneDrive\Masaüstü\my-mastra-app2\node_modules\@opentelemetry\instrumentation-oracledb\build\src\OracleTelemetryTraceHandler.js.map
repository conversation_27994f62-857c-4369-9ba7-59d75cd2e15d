{"version": 3, "file": "OracleTelemetryTraceHandler.js", "sourceRoot": "", "sources": ["../../src/OracleTelemetryTraceHandler.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;;;KAgBK;AACL,oEAAwE;AACxE,4CAQ4B;AAC5B,8EAI6C;AAC7C,uCAOmB;AAInB,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,4BAA4B;AAKnD,2CAAgE;AAEhE,+EAA+E;AAC/E,0EAA0E;AAC1E,yBAAyB;AACzB,SAAS,wBAAwB,CAC/B,GAAyB;IAEzB,IAAI;QACF,OAAQ,GAAW,CAAC,YAAY,CAAC,gBAAwC,CAAC;KAC3E;IAAC,OAAO,GAAG,EAAE;QACZ,UAAI,CAAC,KAAK,CAAC,iCAAiC,EAAE,GAAG,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC;KACb;AACH,CAAC;AAED,SAAgB,mCAAmC,CACjD,GAAyB;IAEzB,MAAM,gBAAgB,GAAG,wBAAwB,CAAC,GAAG,CAAC,CAAC;IACvD,IAAI,CAAC,gBAAgB,EAAE;QACrB,OAAO,SAAS,CAAC;KAClB;IAED;;;;;;OAMG;IACH,MAAM,2BAA4B,SAAQ,gBAAgB;QAChD,UAAU,CAAe;QACzB,iBAAiB,CAA8B;QAEvD,YAAY,SAAuB,EAAE,MAAmC;YACtE,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;YAC5B,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC;QAClC,CAAC;QAEO,0BAA0B;YAChC,OAAO,CACL,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,KAAK,IAAI;gBACjD,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,KAAK,SAAS,CAC9C,CAAC;QACJ,CAAC;QAED,+DAA+D;QAC/D,6CAA6C;QACrC,eAAe,CACrB,YAAqB,EACrB,OAAgB,EAChB,WAAoB;YAEpB,IAAI,YAAY,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI,EAAE;gBAClE,OAAO,SAAS,CAAC;aAClB;YACD,OAAO,GAAG,YAAY,IAAI,EAAE,IAAI,OAAO,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE,CAAC;QACvE,CAAC;QAED,gDAAgD;QAChD,6CAA6C;QACrC,4BAA4B,CAAC,MAA4B;YAC/D,OAAO;gBACL,CAAC,wBAAc,CAAC,EAAE,kCAAsB;gBACxC,CAAC,6CAAsB,CAAC,EAAE,MAAM,CAAC,QAAQ;gBACzC,CAAC,sBAAY,CAAC,EAAE,MAAM,CAAC,IAAI;gBAC3B,CAAC,2BAAiB,CAAC,EAAE,IAAI,CAAC,eAAe,CACvC,MAAM,CAAC,YAAY,EACnB,MAAM,CAAC,OAAO,EACd,MAAM,CAAC,WAAW,CACnB;gBACD,CAAC,0CAAmB,CAAC,EAAE,MAAM,CAAC,QAAQ;gBACtC,CAAC,uCAAgB,CAAC,EAAE,MAAM,CAAC,IAAI;aAChC,CAAC;QACJ,CAAC;QAED,qDAAqD;QAC7C,cAAc,CAAC,GAAY;YACjC,OAAO,CACL,OAAO,GAAG,KAAK,QAAQ;gBACvB,GAAG,KAAK,IAAI;gBACZ,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,WAAW,EAAE,IAAI,KAAK,KAAK,CACzD,CAAC;QACJ,CAAC;QAED,8DAA8D;QAC9D,4BAA4B;QAC5B,MAAM;QACN,iEAAiE;QACjE,gEAAgE;QAChE,mEAAmE;QAC3D,UAAU,CAAC,MAAW;YAC5B,IAAI,CAAC,MAAM;gBAAE,OAAO,SAAS,CAAC;YAC9B,MAAM,eAAe,GAA2B,EAAE,CAAC;YAEnD,IAAI;gBACF,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;oBACzB,yCAAyC;oBACzC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;wBAC9B,MAAM,GAAG,GAAG,GAAG,qCAA2B,IAAI,KAAK,EAAE,CAAC;wBACtD,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;wBACjD,IAAI,cAAc,KAAK,SAAS,EAAE;4BAChC,eAAe,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC;yBACvC;oBACH,CAAC,CAAC,CAAC;iBACJ;qBAAM,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;oBAC/C,0BAA0B;oBAC1B,KAAK,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;wBACvD,MAAM,GAAG,GAAG,GAAG,qCAA2B,IAAI,SAAS,EAAE,CAAC;wBAC1D,IAAI,KAAK,GAAQ,KAAK,CAAC;wBAEvB,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;4BACtC,8BAA8B;4BAC9B,IAAI,KAAK,CAAC,GAAG,KAAK,QAAQ,EAAE;gCAC1B,WAAW;gCACX,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;gCAC1B,SAAS;6BACV;4BACD,IAAI,KAAK,IAAI,KAAK,EAAE;gCAClB,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC;6BACnB;yBACF;wBACD,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;wBACjD,IAAI,cAAc,KAAK,SAAS,EAAE;4BAChC,eAAe,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC;yBACvC;qBACF;iBACF;aACF;YAAC,OAAO,CAAC,EAAE;gBACV,UAAI,CAAC,KAAK,CAAC,kCAAkC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;gBAC1D,OAAO,SAAS,CAAC;aAClB;YACD,OAAO,eAAe,CAAC;QACzB,CAAC;QAEO,aAAa,CAAC,KAAU;YAC9B,IAAI,KAAK,IAAI,IAAI,EAAE;gBACjB,OAAO,MAAM,CAAC;aACf;YACD,IAAI,KAAK,YAAY,MAAM,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;gBACzD,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC;aACzB;YACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC7B,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;aAC9B;YACD,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC1B,CAAC;QAED,6CAA6C;QAC7C,mDAAmD;QACnD,sEAAsE;QAC9D,uBAAuB,CAC7B,IAAU,EACV,UAAgC,EAChC,SAAS,GAAG,KAAK;YAEjB,IAAI,CAAC,UAAU;gBAAE,OAAO;YAExB,IAAI,UAAU,CAAC,SAAS,EAAE;gBACxB,IAAI,CAAC,YAAY,CACf,gCAAsB;gBACtB,+BAA+B;gBAC/B,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CACjD,CAAC;gBACF,IACE,IAAI,CAAC,iBAAiB,CAAC,eAAe;oBACtC,IAAI,CAAC,iBAAiB,CAAC,yBAAyB,EAChD;oBACA,IAAI,CAAC,YAAY,CAAC,2BAAiB,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC;oBAC3D,IAAI,IAAI,CAAC,iBAAiB,CAAC,yBAAyB,IAAI,CAAC,SAAS,EAAE;wBAClE,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;wBAClD,IAAI,MAAM,EAAE;4BACV,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;yBAC5B;qBACF;iBACF;aACF;QACH,CAAC;QAEO,2BAA2B,CACjC,IAAU,EACV,YAA2B;YAE3B,IAAI,OAAO,IAAI,CAAC,iBAAiB,CAAC,WAAW,KAAK,UAAU,EAAE;gBAC5D,IAAA,wCAAsB,EACpB,GAAG,EAAE;oBACH,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE;wBACzC,UAAU,EAAE,YAAY,CAAC,kBAAkB;wBAC3C,SAAS,EAAE,YAAY,CAAC,gBAAgB,CAAC,IAAI;qBAC9C,CAAC,CAAC;gBACL,CAAC,EACD,GAAG,CAAC,EAAE;oBACJ,IAAI,GAAG,EAAE;wBACP,UAAI,CAAC,KAAK,CAAC,4BAA4B,EAAE,GAAG,CAAC,CAAC;qBAC/C;gBACH,CAAC,EACD,IAAI,CACL,CAAC;aACH;QACH,CAAC;QAEO,0BAA0B,CAChC,IAAU,EACV,YAA2B;YAE3B,IAAI,OAAO,IAAI,CAAC,iBAAiB,CAAC,YAAY,KAAK,UAAU,EAAE;gBAC7D,IAAA,wCAAsB,EACpB,GAAG,EAAE;oBACH,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,CAAC,IAAI,EAAE;wBAC1C,IAAI,EAAE,YAAY,CAAC,gBAAgB,CAAC,MAAM;qBAC3C,CAAC,CAAC;gBACL,CAAC,EACD,GAAG,CAAC,EAAE;oBACJ,IAAI,GAAG,EAAE;wBACP,UAAI,CAAC,KAAK,CAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC;qBAC7C;gBACH,CAAC,EACD,IAAI,CACL,CAAC;aACH;QACH,CAAC;QAED,4CAA4C;QAC5C,2CAA2C;QAC3C,oFAAoF;QACpF,qFAAqF;QAC7E,eAAe,CAAC,YAA2B;YACjD,MAAM,EAAE,kBAAkB,EAAE,eAAe,EAAE,WAAW,EAAE,SAAS,EAAE,GACnE,YAAY,CAAC;YACf,IACE,CAAC;gBACC,qBAAS,CAAC,OAAO;gBACjB,qBAAS,CAAC,YAAY;gBACtB,qBAAS,CAAC,WAAW;aACtB,CAAC,QAAQ,CAAC,SAAsB,CAAC,EAClC;gBACA,iDAAiD;gBACjD,OAAO;aACR;YAED,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,kBAAkB,CAAC;YAClE,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;YACxE,MAAM,UAAU,GACd,eAAe,EAAE,SAAS,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC;YAChE,WAAW,CAAC,IAAI,CAAC,UAAU,CACzB,GAAG,SAAS,IAAI,UAAU,GAAG,MAAM,IAAI,IAAI,MAAM,EAAE,EAAE,CACtD,CAAC;QACJ,CAAC;QAED,sDAAsD;QACtD,sDAAsD;QACtD,mDAAmD;QACnD,6DAA6D;QACrD,0BAA0B,CAChC,YAA2B,EAC3B,SAAS,GAAG,KAAK;YAEjB,MAAM,IAAI,GAAG,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC;YAC3C,mDAAmD;YACnD,gBAAgB;YAChB,IAAI,YAAY,CAAC,kBAAkB,EAAE;gBACnC,IAAI,CAAC,aAAa,CAChB,IAAI,CAAC,4BAA4B,CAAC,YAAY,CAAC,kBAAkB,CAAC,CACnE,CAAC;aACH;YACD,IAAI,YAAY,CAAC,eAAe,EAAE;gBAChC,IAAI,CAAC,uBAAuB,CAC1B,IAAI,EACJ,YAAY,CAAC,eAAe,EAC5B,SAAS,CACV,CAAC;aACH;YACD,IAAI,YAAY,CAAC,KAAK,EAAE;gBACtB,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACzC,IAAI,CAAC,SAAS,CAAC;oBACb,IAAI,EAAE,oBAAc,CAAC,KAAK;oBAC1B,OAAO,EAAE,YAAY,CAAC,KAAK,CAAC,OAAO;iBACpC,CAAC,CAAC;aACJ;QACH,CAAC;QAED,mBAAmB,CAAC,SAAsC,EAAE;YAC1D,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC;QAClC,CAAC;QAED,6DAA6D;QAC7D,wBAAwB;QACxB,SAAS,CAAC,YAA2B;YACnC,IAAI,IAAI,CAAC,0BAA0B,EAAE,EAAE;gBACrC,OAAO;aACR;YAED,MAAM,QAAQ,GAAG,YAAY,CAAC,SAAS,CAAC;YACxC,MAAM,cAAc,GAAG,YAAY,CAAC,kBAAkB;gBACpD,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,YAAY,CAAC,kBAAkB,CAAC;gBACpE,CAAC,CAAC,EAAE,CAAC;YAEP,YAAY,CAAC,WAAW,GAAG;gBACzB,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE;oBAC1C,IAAI,EAAE,cAAQ,CAAC,MAAM;oBACrB,UAAU,EAAE,cAAc;iBAC3B,CAAC;aACH,CAAC;YAEF,IAAI,YAAY,CAAC,EAAE,EAAE;gBACnB,yDAAyD;gBACzD,YAAY,CAAC,EAAE,GAAG,aAAO,CAAC,IAAI,CAC5B,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,EAC9D,YAAY,CAAC,EAAE,CAChB,CAAC;aACH;YAED,IAAI,YAAY,CAAC,SAAS,KAAK,qBAAS,CAAC,OAAO,EAAE;gBAChD,IAAI,CAAC,2BAA2B,CAC9B,YAAY,CAAC,WAAW,CAAC,IAAI,EAC7B,YAAY,CACb,CAAC;aACH;QACH,CAAC;QAED,sEAAsE;QACtE,aAAa;QACb,QAAQ,CAAC,YAA2B;YAClC,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,IAAI,EAAE;gBACnC,OAAO;aACR;YACD,IAAI,CAAC,0BAA0B,CAAC,YAAY,CAAC,CAAC;YAC9C,QAAQ,YAAY,CAAC,SAAS,EAAE;gBAC9B,KAAK,qBAAS,CAAC,OAAO;oBACpB,IAAI,CAAC,0BAA0B,CAC7B,YAAY,CAAC,WAAW,CAAC,IAAI,EAC7B,YAAY,CACb,CAAC;oBACF,MAAM;gBACR;oBACE,MAAM;aACT;YACD,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;YACnC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;QACtC,CAAC;QAED,gEAAgE;QAChE,qDAAqD;QACrD,gBAAgB,CAAC,YAA2B;YAC1C,IAAI,IAAI,CAAC,0BAA0B,EAAE,EAAE;gBACrC,OAAO;aACR;YACD,MAAM,QAAQ,GAAG,YAAY,CAAC,SAAS,CAAC;YACxC,MAAM,SAAS,GAAG,EAAE,CAAC;YACrB,YAAY,CAAC,WAAW,GAAG;gBACzB,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE;oBAC1C,IAAI,EAAE,cAAQ,CAAC,MAAM;oBACrB,UAAU,EAAE,SAAS;iBACtB,CAAC;aACH,CAAC;QACJ,CAAC;QAED,+DAA+D;QAC/D,qDAAqD;QACrD,cAAc,CAAC,YAA2B;YACxC,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,IAAI,EAAE;gBACnC,OAAO;aACR;YAED,mDAAmD;YACnD,gBAAgB;YAChB,IAAI,CAAC,0BAA0B,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YACpD,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;YACnC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;QACtC,CAAC;KACF;IACD,OAAO,2BAA2B,CAAC;AACrC,CAAC;AAtWD,kFAsWC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * Copyright (c) 2025, Oracle and/or its affiliates.\n * */\nimport { safeExecuteInTheMiddle } from '@opentelemetry/instrumentation';\nimport {\n  Span,\n  SpanStatusCode,\n  Tracer,\n  context,\n  SpanKind,\n  trace,\n  diag,\n} from '@opentelemetry/api';\nimport {\n  ATTR_SERVER_PORT,\n  ATTR_SERVER_ADDRESS,\n  ATTR_NETWORK_TRANSPORT,\n} from '@opentelemetry/semantic-conventions';\nimport {\n  ATTR_DB_SYSTEM,\n  ATTR_DB_NAMESPACE,\n  ATTR_DB_OPERATION_NAME,\n  ATTR_DB_STATEMENT,\n  ATTR_DB_OPERATION_PARAMETER,\n  ATTR_DB_USER,\n} from './semconv';\n\nimport type * as oracleDBTypes from 'oracledb';\ntype TraceHandlerBaseCtor = new () => any;\nconst OUT_BIND = 3003; // bindinfo direction value.\n\n// Local modules.\nimport { OracleInstrumentationConfig, SpanConnectionConfig } from './types';\nimport { TraceSpanData, SpanCallLevelConfig } from './internal-types';\nimport { SpanNames, DB_SYSTEM_VALUE_ORACLE } from './constants';\n\n// It dynamically retrieves the TraceHandlerBase class from the oracledb module\n// (if available) while avoiding direct imports that could cause issues if\n// the module is missing.\nfunction getTraceHandlerBaseClass(\n  obj: typeof oracleDBTypes\n): TraceHandlerBaseCtor | null {\n  try {\n    return (obj as any).traceHandler.TraceHandlerBase as TraceHandlerBaseCtor;\n  } catch (err) {\n    diag.error('Failed to load oracledb module.', err);\n    return null;\n  }\n}\n\nexport function getOracleTelemetryTraceHandlerClass(\n  obj: typeof oracleDBTypes\n): any {\n  const traceHandlerBase = getTraceHandlerBaseClass(obj);\n  if (!traceHandlerBase) {\n    return undefined;\n  }\n\n  /**\n   * OracleTelemetryTraceHandler extends TraceHandlerBase from oracledb module\n   * It implements the abstract methods; `onEnterFn`, `onExitFn`,\n   * `onBeginRoundTrip` and `onEndRoundTrip` of TraceHandlerBase class.\n   * Inside these overridden methods, the input traceContext data is used\n   * to generate attributes for span.\n   */\n  class OracleTelemetryTraceHandler extends traceHandlerBase {\n    private _getTracer: () => Tracer;\n    private _instrumentConfig: OracleInstrumentationConfig;\n\n    constructor(getTracer: () => Tracer, config: OracleInstrumentationConfig) {\n      super();\n      this._getTracer = getTracer;\n      this._instrumentConfig = config;\n    }\n\n    private _shouldSkipInstrumentation() {\n      return (\n        this._instrumentConfig.requireParentSpan === true &&\n        trace.getSpan(context.active()) === undefined\n      );\n    }\n\n    // It returns db.namespace as mentioned in semantic conventions\n    // Ex: ORCL1|PDB1|db_high.adb.oraclecloud.com\n    private _getDBNameSpace(\n      instanceName?: string,\n      pdbName?: string,\n      serviceName?: string\n    ): string | undefined {\n      if (instanceName == null && pdbName == null && serviceName == null) {\n        return undefined;\n      }\n      return `${instanceName ?? ''}|${pdbName ?? ''}|${serviceName ?? ''}`;\n    }\n\n    // Returns the connection related Attributes for\n    // semantic standards and module custom keys.\n    private _getConnectionSpanAttributes(config: SpanConnectionConfig) {\n      return {\n        [ATTR_DB_SYSTEM]: DB_SYSTEM_VALUE_ORACLE,\n        [ATTR_NETWORK_TRANSPORT]: config.protocol,\n        [ATTR_DB_USER]: config.user,\n        [ATTR_DB_NAMESPACE]: this._getDBNameSpace(\n          config.instanceName,\n          config.pdbName,\n          config.serviceName\n        ),\n        [ATTR_SERVER_ADDRESS]: config.hostName,\n        [ATTR_SERVER_PORT]: config.port,\n      };\n    }\n\n    // It returns true if object is of type oracledb.Lob.\n    private _isLobInstance(obj: unknown): boolean {\n      return (\n        typeof obj === 'object' &&\n        obj !== null &&\n        Reflect.getPrototypeOf(obj)?.constructor?.name === 'Lob'\n      );\n    }\n\n    // Transforms the bind values array or bindinfo into an object\n    // 'db.operation.parameter'.\n    // Ex:\n    //   db.operation.parameter.0 = \"someval\" // for bind by position\n    //   db.operation.parameter.name = \"someval\" // for bind by name\n    // It is only called if config 'enhancedDatabaseReporting' is true.\n    private _getValues(values: any) {\n      if (!values) return undefined;\n      const convertedValues: Record<string, string> = {};\n\n      try {\n        if (Array.isArray(values)) {\n          // Handle indexed (positional) parameters\n          values.forEach((value, index) => {\n            const key = `${ATTR_DB_OPERATION_PARAMETER}.${index}`;\n            const extractedValue = this._extractValue(value);\n            if (extractedValue !== undefined) {\n              convertedValues[key] = extractedValue;\n            }\n          });\n        } else if (values && typeof values === 'object') {\n          // Handle named parameters\n          for (const [paramName, value] of Object.entries(values)) {\n            const key = `${ATTR_DB_OPERATION_PARAMETER}.${paramName}`;\n            let inVal: any = value;\n\n            if (inVal && typeof inVal === 'object') {\n              // Check bind info if present.\n              if (inVal.dir === OUT_BIND) {\n                // outbinds\n                convertedValues[key] = '';\n                continue;\n              }\n              if ('val' in inVal) {\n                inVal = inVal.val;\n              }\n            }\n            const extractedValue = this._extractValue(inVal);\n            if (extractedValue !== undefined) {\n              convertedValues[key] = extractedValue;\n            }\n          }\n        }\n      } catch (e) {\n        diag.error('failed to stringify bind values:', values, e);\n        return undefined;\n      }\n      return convertedValues;\n    }\n\n    private _extractValue(value: any): string | undefined {\n      if (value == null) {\n        return 'null';\n      }\n      if (value instanceof Buffer || this._isLobInstance(value)) {\n        return value.toString();\n      }\n      if (typeof value === 'object') {\n        return JSON.stringify(value);\n      }\n      return value.toString();\n    }\n\n    // Updates the call level attributes in span.\n    // roundTrip flag will skip dumping bind values for\n    // internal roundtrip spans generated for oracledb exported functions.\n    private _setCallLevelAttributes(\n      span: Span,\n      callConfig?: SpanCallLevelConfig,\n      roundTrip = false\n    ) {\n      if (!callConfig) return;\n\n      if (callConfig.statement) {\n        span.setAttribute(\n          ATTR_DB_OPERATION_NAME,\n          // retrieve just the first word\n          callConfig.statement.split(' ')[0].toUpperCase()\n        );\n        if (\n          this._instrumentConfig.dbStatementDump ||\n          this._instrumentConfig.enhancedDatabaseReporting\n        ) {\n          span.setAttribute(ATTR_DB_STATEMENT, callConfig.statement);\n          if (this._instrumentConfig.enhancedDatabaseReporting && !roundTrip) {\n            const values = this._getValues(callConfig.values);\n            if (values) {\n              span.setAttributes(values);\n            }\n          }\n        }\n      }\n    }\n\n    private _handleExecuteCustomRequest(\n      span: Span,\n      traceContext: TraceSpanData\n    ) {\n      if (typeof this._instrumentConfig.requestHook === 'function') {\n        safeExecuteInTheMiddle(\n          () => {\n            this._instrumentConfig.requestHook?.(span, {\n              connection: traceContext.connectLevelConfig,\n              inputArgs: traceContext.additionalConfig.args,\n            });\n          },\n          err => {\n            if (err) {\n              diag.error('Error running request hook', err);\n            }\n          },\n          true\n        );\n      }\n    }\n\n    private _handleExecuteCustomResult(\n      span: Span,\n      traceContext: TraceSpanData\n    ) {\n      if (typeof this._instrumentConfig.responseHook === 'function') {\n        safeExecuteInTheMiddle(\n          () => {\n            this._instrumentConfig.responseHook?.(span, {\n              data: traceContext.additionalConfig.result,\n            });\n          },\n          err => {\n            if (err) {\n              diag.error('Error running query hook', err);\n            }\n          },\n          true\n        );\n      }\n    }\n\n    // Updates the spanName following the format\n    // {FunctionName:[sqlCommand] db.namespace}\n    // Ex: 'oracledb.Pool.getConnection:[SELECT] ORCL1|PDB1|db_high.adb.oraclecloud.com'\n    // This function is called when connectLevelConfig has required parameters populated.\n    private _updateSpanName(traceContext: TraceSpanData) {\n      const { connectLevelConfig, callLevelConfig, userContext, operation } =\n        traceContext;\n      if (\n        ![\n          SpanNames.EXECUTE,\n          SpanNames.EXECUTE_MANY,\n          SpanNames.EXECUTE_MSG,\n        ].includes(operation as SpanNames)\n      ) {\n        // Ignore for connection establishment functions.\n        return;\n      }\n\n      const { instanceName, pdbName, serviceName } = connectLevelConfig;\n      const dbName = this._getDBNameSpace(instanceName, pdbName, serviceName);\n      const sqlCommand =\n        callLevelConfig?.statement?.split(' ')[0].toUpperCase() || '';\n      userContext.span.updateName(\n        `${operation}:${sqlCommand}${dbName && ` ${dbName}`}`\n      );\n    }\n\n    // Updates the span with final traceContext attributes\n    // which are updated after the exported function call.\n    // roundTrip flag will skip dumping bind values for\n    // internal roundtrip spans generated for exported functions.\n    private _updateFinalSpanAttributes(\n      traceContext: TraceSpanData,\n      roundTrip = false\n    ) {\n      const span = traceContext.userContext.span;\n      // Set if additional connection and call parameters\n      // are available\n      if (traceContext.connectLevelConfig) {\n        span.setAttributes(\n          this._getConnectionSpanAttributes(traceContext.connectLevelConfig)\n        );\n      }\n      if (traceContext.callLevelConfig) {\n        this._setCallLevelAttributes(\n          span,\n          traceContext.callLevelConfig,\n          roundTrip\n        );\n      }\n      if (traceContext.error) {\n        span.recordException(traceContext.error);\n        span.setStatus({\n          code: SpanStatusCode.ERROR,\n          message: traceContext.error.message,\n        });\n      }\n    }\n\n    setInstrumentConfig(config: OracleInstrumentationConfig = {}) {\n      this._instrumentConfig = config;\n    }\n\n    // This method is invoked before calling an exported function\n    // from oracledb module.\n    onEnterFn(traceContext: TraceSpanData) {\n      if (this._shouldSkipInstrumentation()) {\n        return;\n      }\n\n      const spanName = traceContext.operation;\n      const spanAttributes = traceContext.connectLevelConfig\n        ? this._getConnectionSpanAttributes(traceContext.connectLevelConfig)\n        : {};\n\n      traceContext.userContext = {\n        span: this._getTracer().startSpan(spanName, {\n          kind: SpanKind.CLIENT,\n          attributes: spanAttributes,\n        }),\n      };\n\n      if (traceContext.fn) {\n        // wrap the active span context to the exported function.\n        traceContext.fn = context.bind(\n          trace.setSpan(context.active(), traceContext.userContext.span),\n          traceContext.fn\n        );\n      }\n\n      if (traceContext.operation === SpanNames.EXECUTE) {\n        this._handleExecuteCustomRequest(\n          traceContext.userContext.span,\n          traceContext\n        );\n      }\n    }\n\n    // This method is invoked after exported function from oracledb module\n    // completes.\n    onExitFn(traceContext: TraceSpanData) {\n      if (!traceContext.userContext?.span) {\n        return;\n      }\n      this._updateFinalSpanAttributes(traceContext);\n      switch (traceContext.operation) {\n        case SpanNames.EXECUTE:\n          this._handleExecuteCustomResult(\n            traceContext.userContext.span,\n            traceContext\n          );\n          break;\n        default:\n          break;\n      }\n      this._updateSpanName(traceContext);\n      traceContext.userContext.span.end();\n    }\n\n    // This method is invoked before a round trip call to DB is done\n    // from the oracledb module as part of sql execution.\n    onBeginRoundTrip(traceContext: TraceSpanData) {\n      if (this._shouldSkipInstrumentation()) {\n        return;\n      }\n      const spanName = traceContext.operation;\n      const spanAttrs = {};\n      traceContext.userContext = {\n        span: this._getTracer().startSpan(spanName, {\n          kind: SpanKind.CLIENT,\n          attributes: spanAttrs,\n        }),\n      };\n    }\n\n    // This method is invoked after a round trip call to DB is done\n    // from the oracledb module as part of sql execution.\n    onEndRoundTrip(traceContext: TraceSpanData) {\n      if (!traceContext.userContext?.span) {\n        return;\n      }\n\n      // Set if additional connection and call parameters\n      // are available\n      this._updateFinalSpanAttributes(traceContext, true);\n      this._updateSpanName(traceContext);\n      traceContext.userContext.span.end();\n    }\n  }\n  return OracleTelemetryTraceHandler;\n}\n"]}