import { ToolListChangedNotificationSchema } from "@modelcontextprotocol/sdk/types.js";
import { jsonSchema, tool, } from "ai";
/**
 * Watches the MCP client for tool changes and updates the tools object accordingly.
 * @param client The MCP client to watch
 * @returns A record of tool names to their implementations
 */
export async function watchTools(client) {
    const tools = {};
    client.setNotificationHandler(ToolListChangedNotificationSchema, async () => {
        Object.assign(tools, await listTools(client));
    });
    Object.assign(tools, await listTools(client));
    return tools;
}
/**
 * Returns a set of wrapped AI SDK tools from the MCP server.
 * @returns A record of tool names to their implementations
 */
export async function listTools(client) {
    const tools = {};
    const listToolsResult = await client.listTools();
    for (const { name, description, inputSchema } of listToolsResult.tools) {
        const parameters = jsonSchema(inputSchema);
        tools[name] = tool({
            description,
            parameters,
            execute: async (args, options) => {
                options?.abortSignal?.throwIfAborted();
                const result = await client.callTool({
                    name,
                    arguments: args,
                });
                return result;
            },
        });
    }
    return tools;
}
