{"version": 3, "file": "propagator.js", "sourceRoot": "", "sources": ["../../src/propagator.ts"], "names": [], "mappings": ";;;AAiBA;;;;EAIE;AACW,QAAA,mBAAmB,GAAkB;IAChD,GAAG,CAAC,OAAO,EAAE,GAAG;QACd,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,SAAS,CAAC;SAClB;QAED,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAElC,KAAK,MAAM,UAAU,IAAI,IAAI,EAAE;YAC7B,IAAI,UAAU,KAAK,GAAG,IAAI,UAAU,CAAC,WAAW,EAAE,KAAK,GAAG,EAAE;gBAC1D,OAAO,OAAO,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,CAAC;aACxC;SACF;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAI,CAAC,OAAO;QACV,OAAO,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC7C,CAAC;CACF,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors, Aspecto\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { TextMapGetter } from '@opentelemetry/api';\n\n/*\nsame as open telemetry's `defaultTextMapGetter`,\nbut also handle case where header is buffer,\nadding toString() to make sure string is returned\n*/\nexport const bufferTextMapGetter: TextMapGetter = {\n  get(carrier, key) {\n    if (!carrier) {\n      return undefined;\n    }\n\n    const keys = Object.keys(carrier);\n\n    for (const carrierKey of keys) {\n      if (carrierKey === key || carrierKey.toLowerCase() === key) {\n        return carrier[carrierKey]?.toString();\n      }\n    }\n\n    return undefined;\n  },\n\n  keys(carrier) {\n    return carrier ? Object.keys(carrier) : [];\n  },\n};\n"]}