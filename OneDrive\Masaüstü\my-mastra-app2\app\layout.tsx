import './globals.css'
import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: '<PERSON><PERSON> - AI Assistant',
  description: 'Telefon görünümlü AI chat arayüzü',
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="tr">
      <body className="font-sf bg-gray-100 overflow-hidden">
        <div className="h-screen w-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
          <div className="w-full max-w-sm h-full max-h-[800px] bg-white rounded-none md:rounded-3xl shadow-2xl overflow-hidden border-8 border-gray-800 md:border-gray-300">
            {children}
          </div>
        </div>
      </body>
    </html>
  )
}
