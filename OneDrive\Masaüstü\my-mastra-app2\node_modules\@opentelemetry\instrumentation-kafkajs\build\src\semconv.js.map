{"version": 3, "file": "semconv.js", "sourceRoot": "", "sources": ["../../src/semconv.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH;;;;GAIG;AAEH;;;;;;;;;;GAUG;AACU,QAAA,kCAAkC,GAC7C,+BAAwC,CAAC;AAE3C;;;;;;;;;;GAUG;AACU,QAAA,+BAA+B,GAC1C,4BAAqC,CAAC;AAExC;;;;;;GAMG;AACU,QAAA,uCAAuC,GAClD,oCAA6C,CAAC;AAEhD;;;;;;;;GAQG;AACU,QAAA,gCAAgC,GAC3C,6BAAsC,CAAC;AAEzC;;;;GAIG;AACU,QAAA,sCAAsC,GACjD,mCAA4C,CAAC;AAE/C;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;;;GAQG;AACU,QAAA,6BAA6B,GACxC,0BAAmC,CAAC;AAEtC;;;;;GAKG;AACU,QAAA,6BAA6B,GACxC,0BAAmC,CAAC;AAEtC;;;;;;GAMG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;GAEG;AACU,QAAA,sCAAsC,GAAG,SAAkB,CAAC;AAEzE;;GAEG;AACU,QAAA,sCAAsC,GAAG,SAAkB,CAAC;AAEzE;;GAEG;AACU,QAAA,mCAAmC,GAAG,MAAe,CAAC;AAEnE;;GAEG;AACU,QAAA,4BAA4B,GAAG,OAAgB,CAAC;AAE7D;;;;;;;GAOG;AACU,QAAA,yCAAyC,GACpD,oCAA6C,CAAC;AAEhD;;;;;;GAMG;AACU,QAAA,0CAA0C,GACrD,qCAA8C,CAAC;AAEjD;;;;;;GAMG;AACU,QAAA,qCAAqC,GAChD,gCAAyC,CAAC;AAE5C;;;;;;GAMG;AACU,QAAA,iCAAiC,GAC5C,4BAAqC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/*\n * This file contains a copy of unstable semantic convention definitions\n * used by this package.\n * @see https://github.com/open-telemetry/opentelemetry-js/tree/main/semantic-conventions#unstable-semconv\n */\n\n/**\n * The number of messages sent, received, or processed in the scope of the batching operation.\n *\n * @example 0\n * @example 1\n * @example 2\n *\n * @note Instrumentations **SHOULD NOT** set `messaging.batch.message_count` on spans that operate with a single message. When a messaging client library supports both batch and single-message API for the same operation, instrumentations **SHOULD** use `messaging.batch.message_count` for batching APIs and **SHOULD NOT** use it for single-message APIs.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_BATCH_MESSAGE_COUNT =\n  'messaging.batch.message_count' as const;\n\n/**\n * The message destination name\n *\n * @example MyQueue\n * @example MyTopic\n *\n * @note Destination name **SHOULD** uniquely identify a specific queue, topic or other entity within the broker. If\n * the broker doesn't have such notion, the destination name **SHOULD** uniquely identify the broker.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_DESTINATION_NAME =\n  'messaging.destination.name' as const;\n\n/**\n * The identifier of the partition messages are sent to or received from, unique within the `messaging.destination.name`.\n *\n * @example \"1\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_DESTINATION_PARTITION_ID =\n  'messaging.destination.partition.id' as const;\n\n/**\n * Message keys in Kafka are used for grouping alike messages to ensure they're processed on the same partition. They differ from `messaging.message.id` in that they're not unique. If the key is `null`, the attribute **MUST NOT** be set.\n *\n * @example \"myKey\"\n *\n * @note If the key type is not string, it's string representation has to be supplied for the attribute. If the key has no unambiguous, canonical string form, don't include its value.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_KAFKA_MESSAGE_KEY =\n  'messaging.kafka.message.key' as const;\n\n/**\n * A boolean that is true if the message is a tombstone.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_KAFKA_MESSAGE_TOMBSTONE =\n  'messaging.kafka.message.tombstone' as const;\n\n/**\n * The offset of a record in the corresponding Kafka partition.\n *\n * @example 42\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_KAFKA_OFFSET = 'messaging.kafka.offset' as const;\n\n/**\n * The system-specific name of the messaging operation.\n *\n * @example ack\n * @example nack\n * @example send\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_OPERATION_NAME =\n  'messaging.operation.name' as const;\n\n/**\n * A string identifying the type of the messaging operation.\n *\n * @note If a custom value is used, it **MUST** be of low cardinality.\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_OPERATION_TYPE =\n  'messaging.operation.type' as const;\n\n/**\n * The messaging system as identified by the client instrumentation.\n *\n * @note The actual messaging system may differ from the one known by the client. For example, when using Kafka client libraries to communicate with Azure Event Hubs, the `messaging.system` is set to `kafka` based on the instrumentation's best knowledge.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_SYSTEM = 'messaging.system' as const;\n\n/**\n * Enum value \"process\" for attribute {@link ATTR_MESSAGING_OPERATION_TYPE}.\n */\nexport const MESSAGING_OPERATION_TYPE_VALUE_PROCESS = 'process' as const;\n\n/**\n * Enum value \"receive\" for attribute {@link ATTR_MESSAGING_OPERATION_TYPE}.\n */\nexport const MESSAGING_OPERATION_TYPE_VALUE_RECEIVE = 'receive' as const;\n\n/**\n * Enum value \"send\" for attribute {@link ATTR_MESSAGING_OPERATION_TYPE}.\n */\nexport const MESSAGING_OPERATION_TYPE_VALUE_SEND = 'send' as const;\n\n/**\n * Enum value \"kafka\" for attribute {@link ATTR_MESSAGING_SYSTEM}.\n */\nexport const MESSAGING_SYSTEM_VALUE_KAFKA = 'kafka' as const;\n\n/**\n * Number of messages that were delivered to the application.\n *\n * @note Records the number of messages pulled from the broker or number of messages dispatched to the application in push-based scenarios.\n * The metric **SHOULD** be reported once per message delivery. For example, if receiving and processing operations are both instrumented for a single message delivery, this counter is incremented when the message is received and not reported when it is processed.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_MESSAGING_CLIENT_CONSUMED_MESSAGES =\n  'messaging.client.consumed.messages' as const;\n\n/**\n * Duration of messaging operation initiated by a producer or consumer client.\n *\n * @note This metric **SHOULD NOT** be used to report processing duration - processing duration is reported in `messaging.process.duration` metric.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_MESSAGING_CLIENT_OPERATION_DURATION =\n  'messaging.client.operation.duration' as const;\n\n/**\n * Number of messages producer attempted to send to the broker.\n *\n * @note This metric **MUST NOT** count messages that were created but haven't yet been sent.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_MESSAGING_CLIENT_SENT_MESSAGES =\n  'messaging.client.sent.messages' as const;\n\n/**\n * Duration of processing operation.\n *\n * @note This metric **MUST** be reported for operations with `messaging.operation.type` that matches `process`.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_MESSAGING_PROCESS_DURATION =\n  'messaging.process.duration' as const;\n"]}