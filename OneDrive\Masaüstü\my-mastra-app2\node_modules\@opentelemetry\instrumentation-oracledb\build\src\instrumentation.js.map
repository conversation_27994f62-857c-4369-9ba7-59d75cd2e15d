{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;;;KAgBK;AACL,oEAGwC;AAGxC,+EAAoF;AACpF,kBAAkB;AAClB,uCAA0D;AAE1D,MAAa,qBAAsB,SAAQ,qCAAmB;IACpD,UAAU,CAAM;IAExB,YAAY,SAAsC,EAAE;QAClD,KAAK,CAAC,sBAAY,EAAE,yBAAe,EAAE,MAAM,CAAC,CAAC;IAC/C,CAAC;IAES,IAAI;QACZ,MAAM,cAAc,GAAG,IAAI,qDAAmC,CAC5D,UAAU,EACV,CAAC,YAAY,CAAC,EACd,CAAC,aAAmC,EAAE,EAAE;YACtC,IAAI,CAAC,aAAa,EAAE;gBAClB,OAAO;aACR;YACD,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,wCAAwC;gBACvC,aAAqB,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;gBACvD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;aACxB;YACD,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YAChC,MAAM,OAAO,GAAG,IAAA,iEAAmC,EAAC,aAAa,CAAC,CAAC;YACnE,IAAI,OAAO,EAAE;gBACX,MAAM,GAAG,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBACnD,GAAG,CAAC,MAAM,EAAE,CAAC;gBAEb,uCAAuC;gBACtC,aAAqB,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;gBAC1D,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;aACvB;YACD,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,aAAa,CAAC,EAAE;YACd,IAAI,IAAI,CAAC,UAAU,EAAE;gBAClB,aAAqB,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;gBACvD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;aACxB;QACH,CAAC,CACF,CAAC;QAEF,OAAO,CAAC,cAAc,CAAC,CAAC;IAC1B,CAAC;IAEQ,SAAS,CAAC,SAAsC,EAAE;QACzD,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAExB,wDAAwD;QACxD,IAAI,CAAC,UAAU,EAAE,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACrD,CAAC;CACF;AAjDD,sDAiDC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * Copyright (c) 2025, Oracle and/or its affiliates.\n * */\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n} from '@opentelemetry/instrumentation';\nimport type * as oracleDBTypes from 'oracledb';\nimport { OracleInstrumentationConfig } from './types';\nimport { getOracleTelemetryTraceHandlerClass } from './OracleTelemetryTraceHandler';\n/** @knipignore */\nimport { PACKAGE_NAME, PACKAGE_VERSION } from './version';\n\nexport class OracleInstrumentation extends InstrumentationBase {\n  private _tmHandler: any;\n\n  constructor(config: OracleInstrumentationConfig = {}) {\n    super(PACKAGE_NAME, PACKAGE_VERSION, config);\n  }\n\n  protected init() {\n    const moduleOracleDB = new InstrumentationNodeModuleDefinition(\n      'oracledb',\n      ['>= 6.7 < 7'],\n      (moduleExports: typeof oracleDBTypes) => {\n        if (!moduleExports) {\n          return;\n        }\n        if (this._tmHandler) {\n          // Already registered, so unregister it.\n          (moduleExports as any).traceHandler.setTraceInstance();\n          this._tmHandler = null;\n        }\n        const config = this.getConfig();\n        const thClass = getOracleTelemetryTraceHandlerClass(moduleExports);\n        if (thClass) {\n          const obj = new thClass(() => this.tracer, config);\n          obj.enable();\n\n          // Register the instance with oracledb.\n          (moduleExports as any).traceHandler.setTraceInstance(obj);\n          this._tmHandler = obj;\n        }\n        return moduleExports;\n      },\n      moduleExports => {\n        if (this._tmHandler) {\n          (moduleExports as any).traceHandler.setTraceInstance();\n          this._tmHandler = null;\n        }\n      }\n    );\n\n    return [moduleOracleDB];\n  }\n\n  override setConfig(config: OracleInstrumentationConfig = {}) {\n    super.setConfig(config);\n\n    // update the config in OracleTelemetryTraceHandler obj.\n    this._tmHandler?.setInstrumentConfig(this._config);\n  }\n}\n"]}