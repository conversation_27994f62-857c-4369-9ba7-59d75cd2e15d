{"version": 3, "file": "constants.js", "sourceRoot": "", "sources": ["../../src/constants.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;KAgBK;;;AAEL,kDAAkD;AAClD,iEAAiE;AACjE,8CAA8C;AAC9C,mEAAmE;AACnE,qBAAqB;AACrB,IAAY,SAgBX;AAhBD,WAAY,SAAS;IACnB,+CAAkC,CAAA;IAClC,yDAA4C,CAAA;IAC5C,gDAAmC,CAAA;IACnC,8DAAiD,CAAA;IACjD,8DAAiD,CAAA;IACjD,sDAAyC,CAAA;IACzC,2DAA8C,CAAA;IAC9C,oDAAuC,CAAA;IACvC,oDAAuC,CAAA;IACvC,6DAAgD,CAAA;IAChD,kDAAqC,CAAA;IACrC,wDAA2C,CAAA;IAC3C,yDAA4C,CAAA;IAC5C,kDAAqC,CAAA;IACrC,iDAAoC,CAAA;AACtC,CAAC,EAhBW,SAAS,GAAT,iBAAS,KAAT,iBAAS,QAgBpB;AAED;;;;GAIG;AACU,QAAA,sBAAsB,GAAG,WAAW,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * Copyright (c) 2025, Oracle and/or its affiliates.\n * */\n\n// Contains span names produced by instrumentation\n// It lists the RPC names (suffix with _MSG like EXECUTE_MSG) and\n// exported oracledb functions (like EXECUTE).\n// These constants need to be in sync with what is generated by the\n// 'oracledb' module.\nexport enum SpanNames {\n  CONNECT = 'oracledb.getConnection',\n  POOL_CONNECT = 'oracledb.Pool.getConnection',\n  POOL_CREATE = 'oracledb.createPool',\n  CONNECT_PROTOCOL_NEG = 'oracledb.ProtocolMessage',\n  CONNECT_DATATYPE_NEG = 'oracledb.DataTypeMessage',\n  CONNECT_AUTH_MSG = 'oracledb.AuthMessage',\n  CONNECT_FAST_AUTH = 'oracledb.FastAuthMessage',\n  EXECUTE_MSG = 'oracledb.ExecuteMessage',\n  EXECUTE = 'oracledb.Connection.execute',\n  EXECUTE_MANY = 'oracledb.Connection.executeMany',\n  LOGOFF_MSG = 'oracledb.LogOffMessage',\n  CONNECT_CLOSE = 'oracledb.Connection.close',\n  CREATE_LOB = 'oracledb.Connection.createLob',\n  LOB_MESSAGE = 'oracledb.LobOpMessage',\n  LOB_GETDATA = 'oracledb.Lob.getData',\n}\n\n/*\n * The semantic conventions defined DBSYSTEMVALUES_ORACLE as oracle, hence\n * defining the new constant to explicitly mention db.\n *\n */\nexport const DB_SYSTEM_VALUE_ORACLE = 'oracle.db';\n"]}