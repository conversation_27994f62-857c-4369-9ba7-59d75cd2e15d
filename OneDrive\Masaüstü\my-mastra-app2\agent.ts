import { MCPClient } from "@mastra/mcp";
import { Agent } from "@mastra/core";

// Smithery MCP endpoint'in, API key varsa sonuna ekle
const mcp = new MCPClient({
  servers: {
    smithery: {
      type: "http",
      url: "https://server.smithery.ai/@meren41/s-navmobil/mcp?api_key=02050232-0fc4-4ada-91d7-aa175ae658a7", // kendi MC<PERSON> endpointinle de<PERSON>
    },
  },
});

// OpenAI istemcisine gerek yok, MCP ile çalışacağız

const agent = new Agent({
  mcpClient: mcp,
  name: "JokeAgent",
  // model: "gpt-4o-mini", // opsiyonel, MCP zaten backend işini yapıyor
});

async function test() {
  // MCP tool'un 'get_joke' ise:
  const response = await agent.invoke({
    toolId: "get_random_joke",      // MCP tool ismini yaz
    input: {},               // get_joke parametre almıyorsa boş obje gönder
  });

  console.log("Joke MCP cevabı:", response);
}

test();
