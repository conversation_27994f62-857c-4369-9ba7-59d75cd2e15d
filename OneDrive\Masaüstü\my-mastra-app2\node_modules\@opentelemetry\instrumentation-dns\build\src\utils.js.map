{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAAsE;AACtE,2DAAwD;AAKxD;;;;;GAKG;AACI,MAAM,QAAQ,GAAG,CAAC,GAA0B,EAAE,IAAU,EAAE,EAAE;IACjE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC;IAC9B,MAAM,UAAU,GAAG;QACjB,CAAC,+BAAc,CAAC,iBAAiB,CAAC,EAAE,OAAO;QAC3C,CAAC,+BAAc,CAAC,cAAc,CAAC,EAAE,IAAI;KACxB,CAAC;IAEhB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IAE/B,IAAI,CAAC,SAAS,CAAC;QACb,IAAI,EAAE,oBAAc,CAAC,KAAK;QAC1B,OAAO;KACR,CAAC,CAAC;AACL,CAAC,CAAC;AAbW,QAAA,QAAQ,YAanB;AAEF;;;;GAIG;AACI,MAAM,kBAAkB,GAAG,CAChC,MAAqB,EACrB,KAAc,EACN,EAAE;IACV,OAAO,KAAK,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,MAAM,EAAE,CAAC,CAAC,CAAC,WAAW,MAAM,EAAE,CAAC;AACrE,CAAC,CAAC;AALW,QAAA,kBAAkB,sBAK7B;AAEF;;;;GAIG;AACI,MAAM,gBAAgB,GAAG,CAC9B,QAAgB,EAChB,OAAgB,EACR,EAAE;IACV,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,OAAO,IAAI,QAAQ,EAAE,CAAC,CAAC,CAAC,OAAO,QAAQ,EAAE,CAAC;AACpE,CAAC,CAAC;AALW,QAAA,gBAAgB,oBAK3B;AAEK,MAAM,mBAAmB,GAAG,CACjC,IAAU,EACV,OAAyD,EACzD,MAAe,EACf,EAAE;IACF,MAAM,UAAU,GAAG,EAAgB,CAAC;IACpC,MAAM,QAAQ,GAAG,OAAO,OAAO,KAAK,QAAQ,CAAC;IAC7C,IAAI,SAAS,GAAG,OAAO,CAAC;IAExB,IAAI,CAAC,QAAQ,EAAE;QACb,SAAS,GAAG,CAAC,EAAE,OAAO,EAAE,MAAM,EAAuB,CAAC,CAAC;KACxD;SAAM,IAAI,CAAC,CAAC,SAAS,YAAY,KAAK,CAAC,EAAE;QACxC,SAAS,GAAG;YACV;gBACE,OAAO,EAAG,OAA6B,CAAC,OAAO;gBAC/C,MAAM,EAAG,OAA6B,CAAC,MAAM;aACzB;SACvB,CAAC;KACH;IAED,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QACzB,MAAM,cAAc,GAAG,IAAA,0BAAkB,EAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACvD,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC;IACzC,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;AACjC,CAAC,CAAC;AA1BW,QAAA,mBAAmB,uBA0B9B;AAEF;;;;;GAKG;AACI,MAAM,gBAAgB,GAAG,CAC9B,QAAgB,EAChB,OAAsB,EACb,EAAE;IACX,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC/B,OAAO,OAAO,KAAK,QAAQ,CAAC;KAC7B;SAAM,IAAI,OAAO,YAAY,MAAM,EAAE;QACpC,OAAO,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC/B;SAAM,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;QACxC,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC;KAC1B;SAAM;QACL,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC;KAC3D;AACH,CAAC,CAAC;AAbW,QAAA,gBAAgB,oBAa3B;AAEF;;;;;;;GAOG;AACI,MAAM,SAAS,GAAG,CACvB,QAAgB,EAChB,IAAsC,EACtC,WAAoC,EAC3B,EAAE;IACX,IAAI,CAAC,IAAI,EAAE;QACT,qCAAqC;QACrC,OAAO,KAAK,CAAC;KACd;IACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QACxB,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;KACf;IACD,8CAA8C;IAC9C,IAAI;QACF,KAAK,MAAM,OAAO,IAAI,IAAI,EAAE;YAC1B,IAAI,IAAA,wBAAgB,EAAC,QAAQ,EAAE,OAAO,CAAC,EAAE;gBACvC,OAAO,IAAI,CAAC;aACb;SACF;KACF;IAAC,OAAO,CAAM,EAAE;QACf,IAAI,WAAW,EAAE;YACf,WAAW,CAAC,CAAC,CAAC,CAAC;SAChB;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AA1BW,QAAA,SAAS,aA0BpB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Span, SpanStatusCode, Attributes } from '@opentelemetry/api';\nimport { AttributeNames } from './enums/AttributeNames';\nimport { AddressFamily } from './enums/AddressFamily';\nimport * as dns from 'dns';\nimport { IgnoreMatcher } from './types';\n\n/**\n * Set error attributes on the span passed in params\n * @param err the error that we use for filling the attributes\n * @param span the span to be set\n * @param nodeVersion the node version\n */\nexport const setError = (err: NodeJS.ErrnoException, span: Span) => {\n  const { message, name } = err;\n  const attributes = {\n    [AttributeNames.DNS_ERROR_MESSAGE]: message,\n    [AttributeNames.DNS_ERROR_NAME]: name,\n  } as Attributes;\n\n  span.setAttributes(attributes);\n\n  span.setStatus({\n    code: SpanStatusCode.ERROR,\n    message,\n  });\n};\n\n/**\n * Returns the family attribute name to be set on the span\n * @param family `4` (ipv4) or `6` (ipv6). `0` means bug.\n * @param [index] `4` (ipv4) or `6` (ipv6). `0` means bug.\n */\nexport const getFamilyAttribute = (\n  family: AddressFamily,\n  index?: number\n): string => {\n  return index ? `peer[${index}].ipv${family}` : `peer.ipv${family}`;\n};\n\n/**\n * Returns the span name\n * @param funcName function name that is wrapped (e.g `lookup`)\n * @param [service] e.g `http`\n */\nexport const getOperationName = (\n  funcName: string,\n  service?: string\n): string => {\n  return service ? `dns.${service}/${funcName}` : `dns.${funcName}`;\n};\n\nexport const setLookupAttributes = (\n  span: Span,\n  address: string | dns.LookupAddress[] | dns.LookupAddress,\n  family?: number\n) => {\n  const attributes = {} as Attributes;\n  const isObject = typeof address === 'object';\n  let addresses = address;\n\n  if (!isObject) {\n    addresses = [{ address, family } as dns.LookupAddress];\n  } else if (!(addresses instanceof Array)) {\n    addresses = [\n      {\n        address: (address as dns.LookupAddress).address,\n        family: (address as dns.LookupAddress).family,\n      } as dns.LookupAddress,\n    ];\n  }\n\n  addresses.forEach((_, i) => {\n    const peerAttrFormat = getFamilyAttribute(_.family, i);\n    attributes[peerAttrFormat] = _.address;\n  });\n\n  span.setAttributes(attributes);\n};\n\n/**\n * Check whether the given obj match pattern\n * @param constant e.g URL of request\n * @param obj obj to inspect\n * @param pattern Match pattern\n */\nexport const satisfiesPattern = (\n  constant: string,\n  pattern: IgnoreMatcher\n): boolean => {\n  if (typeof pattern === 'string') {\n    return pattern === constant;\n  } else if (pattern instanceof RegExp) {\n    return pattern.test(constant);\n  } else if (typeof pattern === 'function') {\n    return pattern(constant);\n  } else {\n    throw new TypeError('Pattern is in unsupported datatype');\n  }\n};\n\n/**\n * Check whether the given dns request is ignored by configuration\n * It will not re-throw exceptions from `list` provided by the client\n * @param constant e.g URL of request\n * @param [list] List of ignore patterns\n * @param [onException] callback for doing something when an exception has\n *     occurred\n */\nexport const isIgnored = (\n  constant: string,\n  list?: IgnoreMatcher | IgnoreMatcher[],\n  onException?: (error: Error) => void\n): boolean => {\n  if (!list) {\n    // No ignored urls - trace everything\n    return false;\n  }\n  if (!Array.isArray(list)) {\n    list = [list];\n  }\n  // Try/catch outside the loop for failing fast\n  try {\n    for (const pattern of list) {\n      if (satisfiesPattern(constant, pattern)) {\n        return true;\n      }\n    }\n  } catch (e: any) {\n    if (onException) {\n      onException(e);\n    }\n  }\n\n  return false;\n};\n"]}