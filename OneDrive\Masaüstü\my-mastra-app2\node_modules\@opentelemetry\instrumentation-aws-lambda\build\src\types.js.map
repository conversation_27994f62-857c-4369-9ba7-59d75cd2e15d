{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Span, Context as OtelContext } from '@opentelemetry/api';\nimport { InstrumentationConfig } from '@opentelemetry/instrumentation';\nimport type { Context } from 'aws-lambda';\n\nexport type RequestHook = (\n  span: Span,\n  hookInfo: { event: any; context: Context }\n) => void;\n\nexport type ResponseHook = (\n  span: Span,\n  hookInfo: {\n    err?: Error | string | null;\n    res?: any;\n  }\n) => void;\n\nexport type EventContextExtractor = (\n  event: any,\n  context: Context\n) => OtelContext;\nexport interface AwsLambdaInstrumentationConfig extends InstrumentationConfig {\n  requestHook?: RequestHook;\n  responseHook?: ResponseHook;\n  eventContextExtractor?: EventContextExtractor;\n  lambdaHandler?: string;\n  lambdaStartTime?: number;\n}\n"]}