{"version": 3, "file": "constants.js", "sourceRoot": "", "sources": ["../../src/constants.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEU,QAAA,iBAAiB,GAAG,MAAM,CACrC,2DAA2D,CAC5D,CAAC;AAEF,gFAAgF;AAChF,6EAA6E;AAC7E,2CAA2C;AAC3C,kDAAkD;AACrC,QAAA,gBAAgB,GAAG,IAAI,GAAG,CAAC;IACtC,WAAW;IACX,WAAW;IACX,YAAY;IACZ,eAAe;IACf,kBAAkB;IAClB,YAAY;IACZ,QAAQ;IACR,YAAY;IACZ,SAAS;CACV,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const spanRequestSymbol = Symbol(\n  'opentelemetry.instrumentation.fastify.request_active_span'\n);\n\n// The instrumentation creates a span for invocations of lifecycle hook handlers\n// that take `(request, reply, ...[, done])` arguments. Currently this is all\n// lifecycle hooks except `onRequestAbort`.\n// https://fastify.dev/docs/latest/Reference/Hooks\nexport const hooksNamesToWrap = new Set([\n  'onTimeout',\n  'onRequest',\n  'preParsing',\n  'preValidation',\n  'preSerialization',\n  'preHandler',\n  'onSend',\n  'onResponse',\n  'onError',\n]);\n"]}