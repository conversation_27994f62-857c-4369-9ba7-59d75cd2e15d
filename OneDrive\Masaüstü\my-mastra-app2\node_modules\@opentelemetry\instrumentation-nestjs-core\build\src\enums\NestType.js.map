{"version": 3, "file": "NestType.js", "sourceRoot": "", "sources": ["../../../src/enums/NestType.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,IAAY,QAIX;AAJD,WAAY,QAAQ;IAClB,yCAA6B,CAAA;IAC7B,+CAAmC,CAAA;IACnC,uCAA2B,CAAA;AAC7B,CAAC,EAJW,QAAQ,GAAR,gBAAQ,KAAR,gBAAQ,QAInB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport enum NestType {\n  APP_CREATION = 'app_creation',\n  REQUEST_CONTEXT = 'request_context',\n  REQUEST_HANDLER = 'handler',\n}\n"]}