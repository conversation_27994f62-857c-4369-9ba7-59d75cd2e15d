{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,8EAM6C;AAS7C;;;;GAIG;AACH,SAAgB,uBAAuB,CACrC,MAA2C;IAE3C,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;IACzD,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IACtC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;QACtB,OAAO;YACL,CAAC,6CAAsB,CAAC,EAAE,IAAI;YAC9B,CAAC,6CAAsB,CAAC,EAAE,UAAU;YACpC,CAAC,oDAA6B,CAAC,EAAE,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC;YACpE,CAAC,uCAAgB,CAAC,EAAE,QAAQ;YAC5B,CAAC,uCAAgB,CAAC,EAAE,IAAI;SACzB,CAAC;KACH;IACD,OAAO;QACL,CAAC,6CAAsB,CAAC,EAAE,IAAI;QAC9B,CAAC,oDAA6B,CAAC,EAAE,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC;QACpE,CAAC,uCAAgB,CAAC,EAAE,QAAQ;QAC5B,CAAC,uCAAgB,CAAC,EAAE,IAAI;KACzB,CAAC;AACJ,CAAC;AApBD,0DAoBC;AAED,SAAS,SAAS,CAAC,MAAW;IAC5B,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAClC,CAAC,MAAM,IAAI,MAAM,CAAC,gBAAgB,CAAC,IAAI,MAAM,IAAI,EAAE,CAAC;IACtD,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AACxC,CAAC;AAED,SAAS,aAAa,CACpB,IAAwB,EACxB,IAAwB,EACxB,QAA4B;IAE5B,IAAI,UAAU,GAAG,gBAAgB,IAAI,IAAI,WAAW,EAAE,CAAC;IAEvD,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QAC5B,UAAU,IAAI,IAAI,IAAI,EAAE,CAAC;KAC1B;IAED,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;QAChC,UAAU,IAAI,IAAI,QAAQ,EAAE,CAAC;KAC9B;IAED,OAAO,UAAU,CAAC;AACpB,CAAC;AAED;;GAEG;AACH,SAAgB,cAAc,CAAC,KAAoC;IACjE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAO,KAAK,CAAC;KACd;SAAM;QACL,OAAO,KAAK,CAAC,GAAG,CAAC;KAClB;AACH,CAAC;AAND,wCAMC;AAED,SAAgB,WAAW,CACzB,KAAoC,EACpC,MAAc;IAEd,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAO,oBAAoB,CAAC,MAAM,CAAC,CAAC;KACrC;SAAM;QACL,mEAAmE;QACnE,qEAAqE;QACrE,OAAO,oBAAoB,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;KACrD;AACH,CAAC;AAXD,kCAWC;AAED;;;;;GAKG;AACH,SAAgB,WAAW,CAAC,KAAoC;IAC9D,MAAM,QAAQ,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;IAC/D,uBAAuB;IACvB,MAAM,UAAU,GAAG,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IAC1C,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE;QACvD,OAAO,QAAQ,EAAE,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;KAC3C;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AARD,kCAQC;AAED,SAAgB,oBAAoB,CAAC,GAA+B;IAClE,IAAI,GAAG;QAAE,OAAO,IAAI,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC;IACtC,OAAO,EAAE,CAAC;AACZ,CAAC;AAHD,oDAGC;AAED,SAAgB,WAAW,CAAC,IAAqB;IAC/C,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC;IACvC,IAAI,QAAQ,GAAG,EAAE,CAAC;IAClB,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;IAChD,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;IAC9C,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;IAC5D,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;IAC9C,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;QACX,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,iBAAiB;KACzE;IACD,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;AACzB,CAAC;AAXD,kCAWC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Attributes } from '@opentelemetry/api';\nimport {\n  SEMATTRS_DB_CONNECTION_STRING,\n  SEMATTRS_DB_NAME,\n  SEMATTRS_DB_USER,\n  SEMATTRS_NET_PEER_NAME,\n  SEMATTRS_NET_PEER_PORT,\n} from '@opentelemetry/semantic-conventions';\nimport type {\n  ConnectionConfig,\n  PoolActualConfig,\n  Query,\n  QueryOptions,\n} from 'mysql';\nimport type * as mysqlTypes from 'mysql';\n\n/**\n * Get an Attributes map from a mysql connection config object\n *\n * @param config ConnectionConfig\n */\nexport function getConnectionAttributes(\n  config: ConnectionConfig | PoolActualConfig\n): Attributes {\n  const { host, port, database, user } = getConfig(config);\n  const portNumber = parseInt(port, 10);\n  if (!isNaN(portNumber)) {\n    return {\n      [SEMATTRS_NET_PEER_NAME]: host,\n      [SEMATTRS_NET_PEER_PORT]: portNumber,\n      [SEMATTRS_DB_CONNECTION_STRING]: getJDBCString(host, port, database),\n      [SEMATTRS_DB_NAME]: database,\n      [SEMATTRS_DB_USER]: user,\n    };\n  }\n  return {\n    [SEMATTRS_NET_PEER_NAME]: host,\n    [SEMATTRS_DB_CONNECTION_STRING]: getJDBCString(host, port, database),\n    [SEMATTRS_DB_NAME]: database,\n    [SEMATTRS_DB_USER]: user,\n  };\n}\n\nfunction getConfig(config: any) {\n  const { host, port, database, user } =\n    (config && config.connectionConfig) || config || {};\n  return { host, port, database, user };\n}\n\nfunction getJDBCString(\n  host: string | undefined,\n  port: number | undefined,\n  database: string | undefined\n) {\n  let jdbcString = `jdbc:mysql://${host || 'localhost'}`;\n\n  if (typeof port === 'number') {\n    jdbcString += `:${port}`;\n  }\n\n  if (typeof database === 'string') {\n    jdbcString += `/${database}`;\n  }\n\n  return jdbcString;\n}\n\n/**\n * @returns the database statement being executed.\n */\nexport function getDbStatement(query: string | Query | QueryOptions): string {\n  if (typeof query === 'string') {\n    return query;\n  } else {\n    return query.sql;\n  }\n}\n\nexport function getDbValues(\n  query: string | Query | QueryOptions,\n  values?: any[]\n): string {\n  if (typeof query === 'string') {\n    return arrayStringifyHelper(values);\n  } else {\n    // According to https://github.com/mysqljs/mysql#performing-queries\n    // The values argument will override the values in the option object.\n    return arrayStringifyHelper(values || query.values);\n  }\n}\n\n/**\n * The span name SHOULD be set to a low cardinality value\n * representing the statement executed on the database.\n *\n * @returns SQL statement without variable arguments or SQL verb\n */\nexport function getSpanName(query: string | Query | QueryOptions): string {\n  const rawQuery = typeof query === 'object' ? query.sql : query;\n  // Extract the SQL verb\n  const firstSpace = rawQuery?.indexOf(' ');\n  if (typeof firstSpace === 'number' && firstSpace !== -1) {\n    return rawQuery?.substring(0, firstSpace);\n  }\n  return rawQuery;\n}\n\nexport function arrayStringifyHelper(arr: Array<unknown> | undefined): string {\n  if (arr) return `[${arr.toString()}]`;\n  return '';\n}\n\nexport function getPoolName(pool: mysqlTypes.Pool): string {\n  const c = pool.config.connectionConfig;\n  let poolName = '';\n  poolName += c.host ? `host: '${c.host}', ` : '';\n  poolName += c.port ? `port: ${c.port}, ` : '';\n  poolName += c.database ? `database: '${c.database}', ` : '';\n  poolName += c.user ? `user: '${c.user}'` : '';\n  if (!c.user) {\n    poolName = poolName.substring(0, poolName.length - 2); //omit last comma\n  }\n  return poolName.trim();\n}\n"]}