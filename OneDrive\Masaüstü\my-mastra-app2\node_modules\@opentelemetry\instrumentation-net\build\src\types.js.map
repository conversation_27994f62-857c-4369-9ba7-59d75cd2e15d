{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,oGAAoG;AACpG,IAAY,aAUX;AAVD,WAAY,aAAa;IACvB,0CAAyB,CAAA;IACzB,8CAA6B,CAAA;IAC7B,gDAA+B,CAAA;IAC/B,sDAAqC,CAAA;IACrC,wEAAuD,CAAA;IACvD,2EAA0D,CAAA;IAC1D,qEAAoD,CAAA;IACpD,iEAAgD,CAAA;IAChD,mDAAkC,CAAA;AACpC,CAAC,EAVW,aAAa,GAAb,qBAAa,KAAb,qBAAa,QAUxB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/* The following attributes are not official, see open-telemetry/opentelemetry-specification#1652 */\nexport enum TLSAttributes {\n  PROTOCOL = 'tls.protocol',\n  AUTHORIZED = 'tls.authorized',\n  CIPHER_NAME = 'tls.cipher.name',\n  CIPHER_VERSION = 'tls.cipher.version',\n  CERTIFICATE_FINGERPRINT = 'tls.certificate.fingerprint',\n  CERTIFICATE_SERIAL_NUMBER = 'tls.certificate.serialNumber',\n  CERTIFICATE_VALID_FROM = 'tls.certificate.validFrom',\n  CERTIFICATE_VALID_TO = 'tls.certificate.validTo',\n  ALPN_PROTOCOL = 'tls.alpnProtocol',\n}\n"]}