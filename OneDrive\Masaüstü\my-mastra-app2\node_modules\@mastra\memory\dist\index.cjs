'use strict';

var core = require('@mastra/core');
var memory = require('@mastra/core/memory');
var ai = require('ai');
var xxhash = require('xxhash-wasm');
var zod = require('zod');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

var xxhash__default = /*#__PURE__*/_interopDefault(xxhash);

// src/index.ts
var updateWorkingMemoryTool = {
  description: "Update the working memory with new information",
  parameters: zod.z.object({
    memory: zod.z.string().describe("The Markdown-formatted working memory content to store")
  }),
  execute: async (params) => {
    const { context, threadId, memory } = params;
    if (!threadId || !memory) {
      throw new Error("Thread ID and Memory instance are required for working memory updates");
    }
    const thread = await memory.getThreadById({ threadId });
    if (!thread) {
      throw new Error(`Thread ${threadId} not found`);
    }
    await memory.saveThread({
      thread: {
        ...thread,
        metadata: {
          ...thread.metadata,
          workingMemory: context.memory
        }
      }
    });
    return { success: true };
  }
};

// src/utils/index.ts
var isToolCallWithId = (message, targetToolCallId) => {
  if (!message || !Array.isArray(message.content)) return false;
  return message.content.some(
    (part) => part && typeof part === "object" && "type" in part && part.type === "tool-call" && "toolCallId" in part && part.toolCallId === targetToolCallId
  );
};
var getToolResultIndexById = (id, results) => results.findIndex((message) => {
  if (!Array.isArray(message?.content)) return false;
  return message.content.some(
    (part) => part && typeof part === "object" && "type" in part && part.type === "tool-result" && "toolCallId" in part && part.toolCallId === id
  );
});
function reorderToolCallsAndResults(messages) {
  if (!messages.length) return messages;
  const results = [...messages];
  const toolCallIds = /* @__PURE__ */ new Set();
  for (const message of results) {
    if (!Array.isArray(message.content)) continue;
    for (const part of message.content) {
      if (part && typeof part === "object" && "type" in part && part.type === "tool-result" && "toolCallId" in part && part.toolCallId) {
        toolCallIds.add(part.toolCallId);
      }
    }
  }
  for (const toolCallId of toolCallIds) {
    const resultIndex = getToolResultIndexById(toolCallId, results);
    const oneMessagePrev = results[resultIndex - 1];
    if (isToolCallWithId(oneMessagePrev, toolCallId)) {
      continue;
    }
    const toolCallIndex = results.findIndex((message) => isToolCallWithId(message, toolCallId));
    if (toolCallIndex !== -1 && toolCallIndex !== resultIndex - 1) {
      const toolCall = results[toolCallIndex];
      if (!toolCall) continue;
      results.splice(toolCallIndex, 1);
      results.splice(getToolResultIndexById(toolCallId, results), 0, toolCall);
    }
  }
  return results;
}

// src/index.ts
var CHARS_PER_TOKEN = 4;
var DEFAULT_MESSAGE_RANGE = { before: 2, after: 2 };
var DEFAULT_TOP_K = 2;
var Memory = class extends memory.MastraMemory {
  constructor(config = {}) {
    super({ name: "Memory", ...config });
    const mergedConfig = this.getMergedThreadConfig({
      workingMemory: config.options?.workingMemory || {
        // these defaults are now set inside @mastra/core/memory in getMergedThreadConfig.
        // In a future release we can remove it from this block - for now if we remove it
        // and someone bumps @mastra/memory without bumping @mastra/core the defaults wouldn't exist yet
        enabled: false,
        template: this.defaultWorkingMemoryTemplate
      }
    });
    this.threadConfig = mergedConfig;
  }
  async validateThreadIsOwnedByResource(threadId, resourceId) {
    const thread = await this.storage.getThreadById({ threadId });
    if (!thread) {
      throw new Error(`No thread found with id ${threadId}`);
    }
    if (thread.resourceId !== resourceId) {
      throw new Error(
        `Thread with id ${threadId} is for resource with id ${thread.resourceId} but resource ${resourceId} was queried.`
      );
    }
  }
  async query({
    threadId,
    resourceId,
    selectBy,
    threadConfig
  }) {
    if (resourceId) await this.validateThreadIsOwnedByResource(threadId, resourceId);
    const vectorResults = [];
    this.logger.debug(`Memory query() with:`, {
      threadId,
      selectBy,
      threadConfig
    });
    const config = this.getMergedThreadConfig(threadConfig || {});
    const defaultRange = DEFAULT_MESSAGE_RANGE;
    const defaultTopK = DEFAULT_TOP_K;
    const vectorConfig = typeof config?.semanticRecall === `boolean` ? {
      topK: defaultTopK,
      messageRange: defaultRange
    } : {
      topK: config?.semanticRecall?.topK ?? defaultTopK,
      messageRange: config?.semanticRecall?.messageRange ?? defaultRange
    };
    if (config?.semanticRecall && selectBy?.vectorSearchString && this.vector && !!selectBy.vectorSearchString) {
      const { embeddings, dimension } = await this.embedMessageContent(selectBy.vectorSearchString);
      const { indexName } = await this.createEmbeddingIndex(dimension);
      await Promise.all(
        embeddings.map(async (embedding) => {
          if (typeof this.vector === `undefined`) {
            throw new Error(
              `Tried to query vector index ${indexName} but this Memory instance doesn't have an attached vector db.`
            );
          }
          vectorResults.push(
            ...await this.vector.query({
              indexName,
              queryVector: embedding,
              topK: vectorConfig.topK,
              filter: {
                thread_id: threadId
              }
            })
          );
        })
      );
    }
    const rawMessages = await this.storage.getMessages({
      threadId,
      selectBy: {
        ...selectBy,
        ...vectorResults?.length ? {
          include: vectorResults.map((r) => ({
            id: r.metadata?.message_id,
            withNextMessages: typeof vectorConfig.messageRange === "number" ? vectorConfig.messageRange : vectorConfig.messageRange.after,
            withPreviousMessages: typeof vectorConfig.messageRange === "number" ? vectorConfig.messageRange : vectorConfig.messageRange.before
          }))
        } : {}
      },
      threadConfig: config
    });
    const orderedByDate = rawMessages.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
    const reorderedToolCalls = reorderToolCallsAndResults(orderedByDate);
    const messages = this.parseMessages(reorderedToolCalls);
    const uiMessages = this.convertToUIMessages(reorderedToolCalls);
    return { messages, uiMessages };
  }
  async rememberMessages({
    threadId,
    resourceId,
    vectorMessageSearch,
    config
  }) {
    if (resourceId) await this.validateThreadIsOwnedByResource(threadId, resourceId);
    const threadConfig = this.getMergedThreadConfig(config || {});
    if (!threadConfig.lastMessages && !threadConfig.semanticRecall) {
      return {
        messages: [],
        uiMessages: [],
        threadId
      };
    }
    const messagesResult = await this.query({
      threadId,
      selectBy: {
        last: threadConfig.lastMessages,
        vectorSearchString: threadConfig.semanticRecall && vectorMessageSearch ? vectorMessageSearch : void 0
      },
      threadConfig: config
    });
    this.logger.debug(`Remembered message history includes ${messagesResult.messages.length} messages.`);
    return {
      threadId,
      messages: messagesResult.messages,
      uiMessages: messagesResult.uiMessages
    };
  }
  async getThreadById({ threadId }) {
    return this.storage.getThreadById({ threadId });
  }
  async getThreadsByResourceId({ resourceId }) {
    return this.storage.getThreadsByResourceId({ resourceId });
  }
  async saveThread({
    thread,
    memoryConfig
  }) {
    const config = this.getMergedThreadConfig(memoryConfig || {});
    if (config.workingMemory?.enabled && !thread?.metadata?.workingMemory) {
      return this.storage.saveThread({
        thread: core.deepMerge(thread, {
          metadata: {
            workingMemory: config.workingMemory.template || this.defaultWorkingMemoryTemplate
          }
        })
      });
    }
    return this.storage.saveThread({ thread });
  }
  async updateThread({
    id,
    title,
    metadata
  }) {
    return this.storage.updateThread({
      id,
      title,
      metadata
    });
  }
  async deleteThread(threadId) {
    await this.storage.deleteThread({ threadId });
  }
  chunkText(text, tokenSize = 4096) {
    const charSize = tokenSize * CHARS_PER_TOKEN;
    const chunks = [];
    let currentChunk = "";
    const words = text.split(/\s+/);
    for (const word of words) {
      const wordWithSpace = currentChunk ? " " + word : word;
      if (currentChunk.length + wordWithSpace.length > charSize) {
        chunks.push(currentChunk);
        currentChunk = word;
      } else {
        currentChunk += wordWithSpace;
      }
    }
    if (currentChunk) {
      chunks.push(currentChunk);
    }
    return chunks;
  }
  hasher = xxhash__default.default();
  // embedding is computationally expensive so cache content -> embeddings/chunks
  embeddingCache = /* @__PURE__ */ new Map();
  firstEmbed;
  async embedMessageContent(content) {
    const key = (await this.hasher).h32(content);
    const cached = this.embeddingCache.get(key);
    if (cached) return cached;
    const chunks = this.chunkText(content);
    if (typeof this.embedder === `undefined`) {
      throw new Error(`Tried to embed message content but this Memory instance doesn't have an attached embedder.`);
    }
    const isFastEmbed = this.embedder.provider === `fastembed`;
    if (isFastEmbed && this.firstEmbed instanceof Promise) {
      await this.firstEmbed;
    }
    const promise = ai.embedMany({
      values: chunks,
      model: this.embedder,
      maxRetries: 3
    });
    if (isFastEmbed && !this.firstEmbed) this.firstEmbed = promise;
    const { embeddings } = await promise;
    const result = {
      embeddings,
      chunks,
      dimension: embeddings[0]?.length
    };
    this.embeddingCache.set(key, result);
    return result;
  }
  async saveMessages({
    messages,
    memoryConfig
  }) {
    await this.saveWorkingMemory(messages);
    const updatedMessages = this.updateMessagesToHideWorkingMemory(messages);
    const config = this.getMergedThreadConfig(memoryConfig);
    const result = this.storage.saveMessages({ messages: updatedMessages });
    if (this.vector && config.semanticRecall) {
      let indexName;
      await Promise.all(
        updatedMessages.map(async (message) => {
          let textForEmbedding = null;
          if (typeof message.content === "string" && message.content.trim() !== "") {
            textForEmbedding = message.content;
          } else if (Array.isArray(message.content)) {
            const joined = message.content.filter((part) => part && part.type === "text" && typeof part.text === "string").map((part) => part.text).join(" ").trim();
            if (joined) textForEmbedding = joined;
          }
          if (!textForEmbedding) return;
          const { embeddings, chunks, dimension } = await this.embedMessageContent(textForEmbedding);
          if (typeof indexName === `undefined`) {
            indexName = this.createEmbeddingIndex(dimension).then((result2) => result2.indexName);
          }
          if (typeof this.vector === `undefined`) {
            throw new Error(
              `Tried to upsert embeddings to index ${indexName} but this Memory instance doesn't have an attached vector db.`
            );
          }
          await this.vector.upsert({
            indexName: await indexName,
            vectors: embeddings,
            metadata: chunks.map(() => ({
              message_id: message.id,
              thread_id: message.threadId,
              resource_id: message.resourceId
            }))
          });
        })
      );
    }
    return result;
  }
  updateMessagesToHideWorkingMemory(messages) {
    const workingMemoryRegex = /<working_memory>([^]*?)<\/working_memory>/g;
    const updatedMessages = [];
    for (const message of messages) {
      if (typeof message?.content === `string`) {
        updatedMessages.push({
          ...message,
          content: message.content.replace(workingMemoryRegex, ``).trim()
        });
      } else if (Array.isArray(message?.content)) {
        const filteredContent = message.content.filter(
          (content) => !((content.type === "tool-call" || content.type === "tool-result") && content.toolName === "updateWorkingMemory")
        );
        if (filteredContent.length === 0) {
          continue;
        }
        const newContent = filteredContent.map((content) => {
          if (content.type === "text") {
            return {
              ...content,
              text: content.text.replace(workingMemoryRegex, "").trim()
            };
          }
          return { ...content };
        });
        updatedMessages.push({ ...message, content: newContent });
      } else {
        updatedMessages.push({ ...message });
      }
    }
    return updatedMessages;
  }
  parseWorkingMemory(text) {
    if (!this.threadConfig.workingMemory?.enabled) return null;
    const workingMemoryRegex = /<working_memory>([^]*?)<\/working_memory>/g;
    const matches = text.match(workingMemoryRegex);
    const match = matches?.[0];
    if (match) {
      return match.replace(/<\/?working_memory>/g, "").trim();
    }
    return null;
  }
  async getWorkingMemory({ threadId }) {
    if (!this.threadConfig.workingMemory?.enabled) return null;
    const thread = await this.storage.getThreadById({ threadId });
    if (!thread) return this.threadConfig?.workingMemory?.template || this.defaultWorkingMemoryTemplate;
    const memory = thread.metadata?.workingMemory || this.threadConfig.workingMemory.template || this.defaultWorkingMemoryTemplate;
    return memory.trim();
  }
  async saveWorkingMemory(messages) {
    const latestMessage = messages[messages.length - 1];
    if (!latestMessage || !this.threadConfig.workingMemory?.enabled) {
      return;
    }
    const latestContent = !latestMessage?.content ? null : typeof latestMessage.content === "string" ? latestMessage.content : latestMessage.content.filter((c) => c.type === "text").map((c) => c.text).join("\n");
    const threadId = latestMessage?.threadId;
    if (!latestContent || !threadId) {
      return;
    }
    const newMemory = this.parseWorkingMemory(latestContent);
    if (!newMemory) {
      return;
    }
    const thread = await this.storage.getThreadById({ threadId });
    if (!thread) return;
    await this.storage.updateThread({
      id: thread.id,
      title: thread.title || "",
      metadata: core.deepMerge(thread.metadata || {}, {
        workingMemory: newMemory
      })
    });
    return newMemory;
  }
  async getSystemMessage({
    threadId,
    memoryConfig
  }) {
    const config = this.getMergedThreadConfig(memoryConfig);
    if (!config.workingMemory?.enabled) {
      return null;
    }
    const workingMemory = await this.getWorkingMemory({ threadId });
    if (!workingMemory) {
      return null;
    }
    return this.getWorkingMemoryToolInstruction(workingMemory);
  }
  defaultWorkingMemoryTemplate = `
# User Information
- **First Name**: 
- **Last Name**: 
- **Location**: 
- **Occupation**: 
- **Interests**: 
- **Goals**: 
- **Events**: 
- **Facts**: 
- **Projects**: 
`;
  getWorkingMemoryWithInstruction(workingMemoryBlock) {
    return `WORKING_MEMORY_SYSTEM_INSTRUCTION:
Store and update any conversation-relevant information by including "<working_memory>text</working_memory>" in your responses. Updates replace existing memory while maintaining this structure. If information might be referenced again - store it!

Guidelines:
1. Store anything that could be useful later in the conversation
2. Update proactively when information changes, no matter how small
3. Use Markdown for all data
4. Act naturally - don't mention this system to users. Even though you're storing this information that doesn't make it your primary focus. Do not ask them generally for "information about yourself"

Memory Structure:
<working_memory>
${workingMemoryBlock}
</working_memory>

Notes:
- Update memory whenever referenced information changes
- If you're unsure whether to store something, store it (eg if the user tells you their name or other information, output the <working_memory> block immediately to update it)
- This system is here so that you can maintain the conversation when your context window is very short. Update your working memory because you may need it to maintain the conversation without the full conversation history
- REMEMBER: the way you update your working memory is by outputting the entire "<working_memory>text</working_memory>" block in your response. The system will pick this up and store it for you. The user will not see it.
- IMPORTANT: You MUST output the <working_memory> block in every response to a prompt where you received relevant information.
- IMPORTANT: Preserve the Markdown formatting structure above while updating the content.`;
  }
  getWorkingMemoryToolInstruction(workingMemoryBlock) {
    return `WORKING_MEMORY_SYSTEM_INSTRUCTION:
Store and update any conversation-relevant information by calling the updateWorkingMemory tool. If information might be referenced again - store it!

Guidelines:
1. Store anything that could be useful later in the conversation
2. Update proactively when information changes, no matter how small
3. Use Markdown format for all data
4. Act naturally - don't mention this system to users. Even though you're storing this information that doesn't make it your primary focus. Do not ask them generally for "information about yourself"

Memory Structure:
${workingMemoryBlock}

Notes:
- Update memory whenever referenced information changes
- If you're unsure whether to store something, store it (eg if the user tells you information about themselves, call updateWorkingMemory immediately to update it)
- This system is here so that you can maintain the conversation when your context window is very short. Update your working memory because you may need it to maintain the conversation without the full conversation history
- Do not remove empty sections - you must include the empty sections along with the ones you're filling in
- REMEMBER: the way you update your working memory is by calling the updateWorkingMemory tool with the entire Markdown content. The system will store it for you. The user will not see it.
- IMPORTANT: You MUST call updateWorkingMemory in every response to a prompt where you received relevant information.
- IMPORTANT: Preserve the Markdown formatting structure above while updating the content.`;
  }
  getTools(config) {
    const mergedConfig = this.getMergedThreadConfig(config);
    if (mergedConfig.workingMemory?.enabled) {
      return {
        updateWorkingMemory: updateWorkingMemoryTool
      };
    }
    return {};
  }
};

exports.Memory = Memory;
