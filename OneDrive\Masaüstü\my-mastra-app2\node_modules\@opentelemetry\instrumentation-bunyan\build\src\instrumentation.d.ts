import { InstrumentationBase, InstrumentationNodeModuleDefinition } from '@opentelemetry/instrumentation';
import { BunyanInstrumentationConfig } from './types';
export declare class BunyanInstrumentation extends InstrumentationBase<BunyanInstrumentationConfig> {
    constructor(config?: BunyanInstrumentationConfig);
    protected init(): InstrumentationNodeModuleDefinition[];
    setConfig(config?: BunyanInstrumentationConfig): void;
    private _getPatchedEmit;
    private _getPatchedCreateLogger;
    private _addStream;
    private _callHook;
}
//# sourceMappingURL=instrumentation.d.ts.map