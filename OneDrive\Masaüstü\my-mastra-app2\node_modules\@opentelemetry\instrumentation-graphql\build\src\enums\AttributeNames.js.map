{"version": 3, "file": "AttributeNames.js", "sourceRoot": "", "sources": ["../../../src/enums/AttributeNames.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,IAAY,cASX;AATD,WAAY,cAAc;IACxB,2CAAyB,CAAA;IACzB,mDAAiC,CAAA;IACjC,mDAAiC,CAAA;IACjC,mDAAiC,CAAA;IACjC,2DAAyC,CAAA;IACzC,2DAAyC,CAAA;IACzC,kDAAgC,CAAA;IAChC,oEAAkD,CAAA;AACpD,CAAC,EATW,cAAc,GAAd,sBAAc,KAAd,sBAAc,QASzB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport enum AttributeNames {\n  SOURCE = 'graphql.source',\n  FIELD_NAME = 'graphql.field.name',\n  FIELD_PATH = 'graphql.field.path',\n  FIELD_TYPE = 'graphql.field.type',\n  OPERATION_TYPE = 'graphql.operation.type',\n  OPERATION_NAME = 'graphql.operation.name',\n  VARIABLES = 'graphql.variables.',\n  ERROR_VALIDATION_NAME = 'graphql.validation.error',\n}\n"]}