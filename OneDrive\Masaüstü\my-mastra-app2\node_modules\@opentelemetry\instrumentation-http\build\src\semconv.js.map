{"version": 3, "file": "semconv.js", "sourceRoot": "", "sources": ["../../src/semconv.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH;;;;GAIG;AAEH;;;;;;GAMG;AACU,QAAA,8BAA8B,GACzC,2BAAoC,CAAC;AAEvC;;GAEG;AACU,QAAA,mCAAmC,GAAG,KAAc,CAAC;AAElE;;GAEG;AACU,QAAA,oCAAoC,GAAG,MAAe,CAAC;AAEpE;;;;;;;;GAQG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;GAMG;AACU,QAAA,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;;;GAQG;AACU,QAAA,cAAc,GAAG,WAAoB,CAAC;AAEnD;;;;;;;;;;GAUG;AACU,QAAA,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;;;GAQG;AACU,QAAA,gCAAgC,GAC3C,6BAAsC,CAAC;AAEzC;;;;;;;;GAQG;AACU,QAAA,6CAA6C,GACxD,0CAAmD,CAAC;AAEtD;;;;;;;;GAQG;AACU,QAAA,iCAAiC,GAC5C,8BAAuC,CAAC;AAE1C;;;;;;;;GAQG;AACU,QAAA,8CAA8C,GACzD,2CAAoD,CAAC;AAEvD;;;;;;;;;GASG;AACU,QAAA,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;;;GAQG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;;;;;;;GAQG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;;;;;;;GAQG;AACU,QAAA,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;;;GAQG;AACU,QAAA,aAAa,GAAG,UAAmB,CAAC;AAEjD;;;;;;;;;GASG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;;;;;GAQG;AACU,QAAA,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;;;GAQG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;;;GAQG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;;;GAQG;AACU,QAAA,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;;;GAQG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;;;GAQG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;GAMG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;GAEG;AACU,QAAA,0BAA0B,GAAG,QAAiB,CAAC;AAE5D;;GAEG;AACU,QAAA,0BAA0B,GAAG,QAAiB,CAAC;AAE5D;;GAEG;AACU,QAAA,0BAA0B,GAAG,KAAc,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/*\n * This file contains a copy of unstable semantic convention definitions\n * used by this package.\n * @see https://github.com/open-telemetry/opentelemetry-js/tree/main/semantic-conventions#unstable-semconv\n */\n\n/**\n * Specifies the category of synthetic traffic, such as tests or bots.\n *\n * @note This attribute **MAY** be derived from the contents of the `user_agent.original` attribute. Components that populate the attribute are responsible for determining what they consider to be synthetic bot or test traffic. This attribute can either be set for self-identification purposes, or on telemetry detected to be generated as a result of a synthetic request. This attribute is useful for distinguishing between genuine client traffic and synthetic traffic generated by bots or tests.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_USER_AGENT_SYNTHETIC_TYPE =\n  'user_agent.synthetic.type' as const;\n\n/**\n * Enum value \"bot\" for attribute {@link ATTR_USER_AGENT_SYNTHETIC_TYPE}.\n */\nexport const USER_AGENT_SYNTHETIC_TYPE_VALUE_BOT = 'bot' as const;\n\n/**\n * Enum value \"test\" for attribute {@link ATTR_USER_AGENT_SYNTHETIC_TYPE}.\n */\nexport const USER_AGENT_SYNTHETIC_TYPE_VALUE_TEST = 'test' as const;\n\n/**\n * Deprecated, use `client.address` instead.\n *\n * @example \"**************\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `client.address`.\n */\nexport const ATTR_HTTP_CLIENT_IP = 'http.client_ip' as const;\n\n/**\n * Deprecated, use `network.protocol.name` instead.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `network.protocol.name`.\n */\nexport const ATTR_HTTP_FLAVOR = 'http.flavor' as const;\n\n/**\n * Deprecated, use one of `server.address`, `client.address` or `http.request.header.host` instead, depending on the usage.\n *\n * @example www.example.org\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by one of `server.address`, `client.address` or `http.request.header.host`, depending on the usage.\n */\nexport const ATTR_HTTP_HOST = 'http.host' as const;\n\n/**\n * Deprecated, use `http.request.method` instead.\n *\n * @example GET\n * @example POST\n * @example HEAD\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `http.request.method`.\n */\nexport const ATTR_HTTP_METHOD = 'http.method' as const;\n\n/**\n * Deprecated, use `http.request.header.<key>` instead.\n *\n * @example 3495\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `http.request.header.<key>`.\n */\nexport const ATTR_HTTP_REQUEST_CONTENT_LENGTH =\n  'http.request_content_length' as const;\n\n/**\n * Deprecated, use `http.request.body.size` instead.\n *\n * @example 5493\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `http.request.body.size`.\n */\nexport const ATTR_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED =\n  'http.request_content_length_uncompressed' as const;\n\n/**\n * Deprecated, use `http.response.header.<key>` instead.\n *\n * @example 3495\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `http.response.header.<key>`.\n */\nexport const ATTR_HTTP_RESPONSE_CONTENT_LENGTH =\n  'http.response_content_length' as const;\n\n/**\n * Deprecated, use `http.response.body.size` instead.\n *\n * @example 5493\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replace by `http.response.body.size`.\n */\nexport const ATTR_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED =\n  'http.response_content_length_uncompressed' as const;\n\n/**\n * Deprecated, use `url.scheme` instead.\n *\n * @example http\n * @example https\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `url.scheme` instead.\n */\nexport const ATTR_HTTP_SCHEME = 'http.scheme' as const;\n\n/**\n * Deprecated, use `server.address` instead.\n *\n * @example example.com\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `server.address`.\n */\nexport const ATTR_HTTP_SERVER_NAME = 'http.server_name' as const;\n\n/**\n * Deprecated, use `http.response.status_code` instead.\n *\n * @example 200\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `http.response.status_code`.\n */\nexport const ATTR_HTTP_STATUS_CODE = 'http.status_code' as const;\n\n/**\n * Deprecated, use `url.path` and `url.query` instead.\n *\n * @example /search?q=OpenTelemetry#SemConv\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Split to `url.path` and `url.query.\n */\nexport const ATTR_HTTP_TARGET = 'http.target' as const;\n\n/**\n * Deprecated, use `url.full` instead.\n *\n * @example https://www.foo.bar/search?q=OpenTelemetry#SemConv\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `url.full`.\n */\nexport const ATTR_HTTP_URL = 'http.url' as const;\n\n/**\n * Deprecated, use `user_agent.original` instead.\n *\n * @example CERN-LineMode/2.15 libwww/2.17b3\n * @example Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `user_agent.original`.\n */\nexport const ATTR_HTTP_USER_AGENT = 'http.user_agent' as const;\n\n/**\n * Deprecated, use `network.local.address`.\n *\n * @example \"***********\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `network.local.address`.\n */\nexport const ATTR_NET_HOST_IP = 'net.host.ip' as const;\n\n/**\n * Deprecated, use `server.address`.\n *\n * @example example.com\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `server.address`.\n */\nexport const ATTR_NET_HOST_NAME = 'net.host.name' as const;\n\n/**\n * Deprecated, use `server.port`.\n *\n * @example 8080\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `server.port`.\n */\nexport const ATTR_NET_HOST_PORT = 'net.host.port' as const;\n\n/**\n * Deprecated, use `network.peer.address`.\n *\n * @example \"127.0.0.1\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `network.peer.address`.\n */\nexport const ATTR_NET_PEER_IP = 'net.peer.ip' as const;\n\n/**\n * Deprecated, use `server.address` on client spans and `client.address` on server spans.\n *\n * @example example.com\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `server.address` on client spans and `client.address` on server spans.\n */\nexport const ATTR_NET_PEER_NAME = 'net.peer.name' as const;\n\n/**\n * Deprecated, use `server.port` on client spans and `client.port` on server spans.\n *\n * @example 8080\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `server.port` on client spans and `client.port` on server spans.\n */\nexport const ATTR_NET_PEER_PORT = 'net.peer.port' as const;\n\n/**\n * Deprecated, use `network.transport`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `network.transport`.\n */\nexport const ATTR_NET_TRANSPORT = 'net.transport' as const;\n\n/**\n * Enum value \"ip_tcp\" for attribute {@link ATTR_NET_TRANSPORT}.\n */\nexport const NET_TRANSPORT_VALUE_IP_TCP = 'ip_tcp' as const;\n\n/**\n * Enum value \"ip_udp\" for attribute {@link ATTR_NET_TRANSPORT}.\n */\nexport const NET_TRANSPORT_VALUE_IP_UDP = 'ip_udp' as const;\n\n/**\n * Enum value \"1.1\" for attribute {@link ATTR_HTTP_FLAVOR}.\n */\nexport const HTTP_FLAVOR_VALUE_HTTP_1_1 = '1.1' as const;\n"]}