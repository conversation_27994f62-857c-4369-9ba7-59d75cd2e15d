{"version": 3, "file": "internal-types.js", "sourceRoot": "", "sources": ["../../src/internal-types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAUH,uCAA0E;AAE7D,QAAA,uBAAuB,GAClC,6BAA6B,GAAG,YAAY,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type * as graphqlTypes from 'graphql';\nimport type * as api from '@opentelemetry/api';\nimport type { PromiseOrValue } from 'graphql/jsutils/PromiseOrValue';\nimport type { DocumentNode } from 'graphql/language/ast';\nimport type {\n  GraphQLFieldResolver,\n  GraphQLTypeResolver,\n} from 'graphql/type/definition';\nimport { OTEL_GRAPHQL_DATA_SYMBOL, OTEL_PATCHED_SYMBOL } from './symbols';\n\nexport const OPERATION_NOT_SUPPORTED =\n  'Operation$operationName$not' + ' supported';\n\nexport type executeFunctionWithObj = (\n  args: graphqlTypes.ExecutionArgs\n) => PromiseOrValue<graphqlTypes.ExecutionResult>;\n\nexport type executeArgumentsArray = [\n  graphqlTypes.GraphQLSchema,\n  graphqlTypes.DocumentNode,\n  any,\n  any,\n  Maybe<{ [key: string]: any }>,\n  Maybe<string>,\n  Maybe<graphqlTypes.GraphQLFieldResolver<any, any>>,\n  Maybe<graphqlTypes.GraphQLTypeResolver<any, any>>\n];\n\nexport type executeFunctionWithArgs = (\n  schema: graphqlTypes.GraphQLSchema,\n  document: graphqlTypes.DocumentNode,\n  rootValue?: any,\n  contextValue?: any,\n  variableValues?: Maybe<{ [key: string]: any }>,\n  operationName?: Maybe<string>,\n  fieldResolver?: Maybe<graphqlTypes.GraphQLFieldResolver<any, any>>,\n  typeResolver?: Maybe<graphqlTypes.GraphQLTypeResolver<any, any>>\n) => PromiseOrValue<graphqlTypes.ExecutionResult>;\n\nexport interface OtelExecutionArgs {\n  schema: graphqlTypes.GraphQLSchema;\n  document: DocumentNode & ObjectWithGraphQLData;\n  rootValue?: any;\n  contextValue?: any & ObjectWithGraphQLData;\n  variableValues?: Maybe<{ [key: string]: any }>;\n  operationName?: Maybe<string>;\n  fieldResolver?: Maybe<GraphQLFieldResolver<any, any> & OtelPatched>;\n  typeResolver?: Maybe<GraphQLTypeResolver<any, any>>;\n}\n\nexport type executeType = executeFunctionWithObj | executeFunctionWithArgs;\n\nexport type parseType = (\n  source: string | graphqlTypes.Source,\n  options?: graphqlTypes.ParseOptions\n) => graphqlTypes.DocumentNode;\n\nexport type validateType = (\n  schema: graphqlTypes.GraphQLSchema,\n  documentAST: graphqlTypes.DocumentNode,\n  rules?: ReadonlyArray<graphqlTypes.ValidationRule>,\n  options?: { maxErrors?: number },\n  typeInfo?: graphqlTypes.TypeInfo\n) => ReadonlyArray<graphqlTypes.GraphQLError>;\n\nexport interface GraphQLField {\n  parent: api.Span;\n  span: api.Span;\n  error: Error | null;\n}\n\ninterface OtelGraphQLData {\n  source?: any;\n  span: api.Span;\n  fields: { [key: string]: GraphQLField };\n}\n\nexport interface ObjectWithGraphQLData {\n  [OTEL_GRAPHQL_DATA_SYMBOL]?: OtelGraphQLData;\n}\n\nexport interface OtelPatched {\n  [OTEL_PATCHED_SYMBOL]?: boolean;\n}\n\nexport interface GraphQLPath {\n  prev: GraphQLPath | undefined;\n  key: string | number;\n  /**\n   * optional as it didn't exist yet in ver 14\n   */\n  typename?: string | undefined;\n}\n\n/**\n * Moving this type from ver 15 of graphql as it is nto available in ver. 14s\n * this way it can compile against ver 14.\n */\nexport type Maybe<T> = null | undefined | T;\n"]}