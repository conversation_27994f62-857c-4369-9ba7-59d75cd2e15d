{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": "", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * Copyright (c) 2025, Oracle and/or its affiliates.\n * */\nimport type * as api from '@opentelemetry/api';\nimport { InstrumentationConfig } from '@opentelemetry/instrumentation';\n\n// Captures connection related span data\nexport interface SpanConnectionConfig {\n  serviceName?: string;\n  connectString?: string;\n  hostName?: string;\n  port?: number;\n  user?: string;\n  protocol?: string;\n  instanceName?: string;\n  serverMode?: string;\n  pdbName?: string;\n  poolMin?: number;\n  poolMax?: number;\n  poolIncrement?: number;\n}\n\nexport interface OracleRequestHookInformation {\n  inputArgs: any;\n  connection: SpanConnectionConfig;\n}\n\nexport interface OracleInstrumentationExecutionRequestHook {\n  (span: api.Span, queryInfo: OracleRequestHookInformation): void;\n}\n\nexport interface OracleResponseHookInformation {\n  data: any; // the result of sql execution.\n}\n\nexport interface OracleInstrumentationExecutionResponseHook {\n  (span: api.Span, resultInfo: OracleResponseHookInformation): void;\n}\n\nexport interface OracleInstrumentationConfig extends InstrumentationConfig {\n  /**\n   * If true, an attribute containing the execute method\n   * bind values will be attached the spans generated.\n   * It can potentially record PII data and should be used with caution.\n   *\n   * @default false\n   */\n  enhancedDatabaseReporting?: boolean;\n\n  /**\n   * If true, db.statement will have sql which could potentially contain\n   * sensitive unparameterized data in the spans generated.\n   *\n   * @default false\n   */\n  dbStatementDump?: boolean;\n\n  /**\n   * Hook that allows adding custom span attributes or updating the\n   * span's name based on the data about the query to execute.\n   *\n   * @default undefined\n   */\n  requestHook?: OracleInstrumentationExecutionRequestHook;\n\n  /**\n   * Hook that allows adding custom span attributes based on the data\n   * returned from \"execute\" actions.\n   *\n   * @default undefined\n   */\n  responseHook?: OracleInstrumentationExecutionResponseHook;\n\n  /**\n   * If true, requires a parent span to create new spans.\n   *\n   * @default false\n   */\n  requireParentSpan?: boolean;\n}\n"]}