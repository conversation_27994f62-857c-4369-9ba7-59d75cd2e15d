{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,8EAG6C;AAC7C,2BAA8B;AAEjB,QAAA,aAAa,GACxB,IAAA,aAAQ,GAAE,KAAK,OAAO,CAAC,CAAC,CAAC,8CAAuB,CAAC,CAAC,CAAC,8CAAuB,CAAC;AAE7E,SAAS,OAAO,CAAC,IAAe;IAC9B,OAAO,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;AAC7D,CAAC;AAED,SAAgB,iBAAiB,CAC/B,IAAe;IAEf,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,IAAI,CAAC,GAAG,EAAE;QACR,OAAO;KACR;IAED,QAAQ,OAAO,GAAG,EAAE;QAClB,KAAK,QAAQ;YACX,OAAO;gBACL,IAAI,EAAE,GAAG;gBACT,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC;aACpB,CAAC;QACJ,KAAK,QAAQ;YACX,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACtB,OAAO,iBAAiB,CAAC,GAAG,CAAC,CAAC;aAC/B;YACD,OAAO,GAAG,CAAC;QACb,KAAK,QAAQ,CAAC,CAAC;YACb,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAC9B,IAAI,SAAS,IAAI,CAAC,EAAE;gBAClB,OAAO;oBACL,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC;iBACpB,CAAC;aACH;YAED,OAAO;gBACL,IAAI,EAAE,GAAG;aACV,CAAC;SACH;QACD;YACE,OAAO;KACV;AACH,CAAC;AAnCD,8CAmCC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NormalizedOptions } from './internal-types';\nimport {\n  NETTRANSPORTVALUES_PIPE,\n  NETTRANSPORTVALUES_UNIX,\n} from '@opentelemetry/semantic-conventions';\nimport { platform } from 'os';\n\nexport const IPC_TRANSPORT =\n  platform() === 'win32' ? NETTRANSPORTVALUES_PIPE : NETTRANSPORTVALUES_UNIX;\n\nfunction getHost(args: unknown[]) {\n  return typeof args[1] === 'string' ? args[1] : 'localhost';\n}\n\nexport function getNormalizedArgs(\n  args: unknown[]\n): NormalizedOptions | null | undefined {\n  const opt = args[0];\n  if (!opt) {\n    return;\n  }\n\n  switch (typeof opt) {\n    case 'number':\n      return {\n        port: opt,\n        host: getHost(args),\n      };\n    case 'object':\n      if (Array.isArray(opt)) {\n        return getNormalizedArgs(opt);\n      }\n      return opt;\n    case 'string': {\n      const maybePort = Number(opt);\n      if (maybePort >= 0) {\n        return {\n          port: maybePort,\n          host: getHost(args),\n        };\n      }\n\n      return {\n        path: opt,\n      };\n    }\n    default:\n      return;\n  }\n}\n"]}