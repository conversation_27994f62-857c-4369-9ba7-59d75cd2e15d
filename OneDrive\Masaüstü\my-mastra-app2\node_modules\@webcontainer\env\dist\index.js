var c=Object.defineProperty;var g=Object.getOwnPropertyDescriptor;var y=Object.getOwnPropertyNames;var U=Object.prototype.hasOwnProperty;var n=(e,t)=>c(e,"name",{value:t,configurable:!0});var b=(e,t)=>{for(var o in t)c(e,o,{get:t[o],enumerable:!0})},L=(e,t,o,h)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of y(t))!U.call(e,s)&&s!==o&&c(e,s,{get:()=>t[s],enumerable:!(h=g(t,s))||h.enumerable});return e};var P=e=>L(c({},"__esModule",{value:!0}),e);var R={};b(R,{HostURL:()=>_,isWebContainer:()=>a});module.exports=P(R);var r;try{r=require("@blitz/internal/env")}catch{}function a(){return r!=null&&process.versions.webcontainer!=null}n(a,"isWebContainer");function u(e){let t=r==null?void 0:r.createServiceHostname(e);if(!t)throw new Error("Failed to construct service hostname");return t}n(u,"_createServiceHostname");function f(e){return r==null?void 0:r.isServiceUrl(e)}n(f,"_isServiceUrl");function p(e){return r==null?void 0:r.isLocalhost(e)}n(p,"_isLocalhost");var l=class l{constructor(t){this._url=t;if(this._port=this._url.port,a()&&p(this._url.hostname)){let o=u(this._port);this._url.host=o,this._port===this._url.port&&(this._url.port="")}}static parse(t){return t=typeof t=="string"?new URL(t):t,new l(t)}get port(){return a()?this._port:this._url.port}get hash(){return this._url.hash}get host(){return this._url.host}get hostname(){return this._url.hostname}get href(){return this._url.href}get origin(){return this._url.origin}get username(){return this._url.username}get password(){return this._url.password}get pathname(){return this._url.pathname}get protocol(){return a()&&f(this._url.hostname)?"https:":this._url.protocol}get search(){return this._url.search}get searchParams(){return this._url.searchParams}update(t){var h;let o=a();for(let s in t){let i=(h=t[s])!=null?h:"";if(o)switch(s){case"port":{if(this._port=i,(p(this._url.hostname)||f(this._url.hostname))&&(this._url.host=u(i),this._port!==this._url.port))continue;break}case"host":{let[d,m=this._port]=i.split(":");this._port=m,p(d)&&(i=u(m));break}case"hostname":{if(p(i)){if(/\/|:/.test(i))continue;i=u(this._port)}else this._url.port=this._port;break}case"href":{this._url=l.parse(i);continue}}this._url[s]=i}return this}patchedURL(){let t=new URL(this._url);return t.protocol=this.protocol,t}toString(){return this.patchedURL().toString()}toJSON(){return this.patchedURL().toJSON()}};n(l,"HostURL");var _=l;0&&(module.exports={HostURL,isWebContainer});
/**
 * @license Copyright 2022 Stackblitz, Inc. All Rights Reserved.
 * Portions of this software are patent pending in USA and EU jurisdictions.
 * More info available at https://stackblitz.com/terms-of-service.
 */
