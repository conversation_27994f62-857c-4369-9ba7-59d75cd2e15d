{"version": 3, "file": "internal-types.js", "sourceRoot": "", "sources": ["../../src/internal-types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAQH,IAAY,WAKX;AALD,WAAY,WAAW;IACrB,8BAAe,CAAA;IACf,kCAAmB,CAAA;IACnB,8BAAe,CAAA;IACf,+CAAgC,CAAA;AAClC,CAAC,EALW,WAAW,GAAX,mBAAW,KAAX,mBAAW,QAKtB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface NormalizedOptions {\n  host?: string;\n  port?: number;\n  path?: string;\n}\n\nexport enum SocketEvent {\n  CLOSE = 'close',\n  CONNECT = 'connect',\n  ERROR = 'error',\n  SECURE_CONNECT = 'secureConnect',\n}\n"]}