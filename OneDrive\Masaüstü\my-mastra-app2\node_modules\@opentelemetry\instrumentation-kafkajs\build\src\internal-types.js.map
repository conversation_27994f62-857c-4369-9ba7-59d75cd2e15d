{"version": 3, "file": "internal-types.js", "sourceRoot": "", "sources": ["../../src/internal-types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAIU,QAAA,mBAAmB,GAAG,MAAM,CACvC,yDAAyD,CAC1D,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors, Aspecto\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type * as KafkaJSTypes from 'kafkajs';\n\nexport const EVENT_LISTENERS_SET = Symbol(\n  'opentelemetry.instrumentation.kafkajs.eventListenersSet'\n);\n\nexport interface ConsumerExtended extends KafkaJSTypes.Consumer {\n  [EVENT_LISTENERS_SET]?: boolean; // flag to identify if the event listeners for instrumentation have been set\n}\n\nexport interface ProducerExtended extends KafkaJSTypes.Producer {\n  [EVENT_LISTENERS_SET]?: boolean; // flag to identify if the event listeners for instrumentation have been set\n}\n"]}