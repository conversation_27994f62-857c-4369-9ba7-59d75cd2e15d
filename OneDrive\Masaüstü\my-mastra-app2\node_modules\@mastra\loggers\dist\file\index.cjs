'use strict';

var fs = require('fs');
var logger = require('@mastra/core/logger');

// src/file/index.ts
var FileTransport = class extends logger.LoggerTransport {
  path;
  fileStream;
  constructor({ path }) {
    super({ objectMode: true });
    this.path = path;
    if (!fs.existsSync(this.path)) {
      console.log(this.path);
      throw new Error("File path does not exist");
    }
    this.fileStream = fs.createWriteStream(this.path, { flags: "a" });
  }
  _transform(chunk, _encoding, callback) {
    try {
      this.fileStream.write(chunk);
    } catch (error) {
      console.error("Error parsing log entry:", error);
    }
    callback(null, chunk);
  }
  _flush(callback) {
    this.fileStream.end(() => {
      callback();
    });
  }
  _write(chunk, encoding, callback) {
    if (typeof callback === "function") {
      this._transform(chunk, encoding || "utf8", callback);
      return true;
    }
    this._transform(chunk, encoding || "utf8", (error) => {
      if (error) console.error("Transform error in write:", error);
    });
    return true;
  }
  // Clean up resources
  _destroy(error, callback) {
    if (this.fileStream) {
      this.fileStream.destroy(error);
    }
    callback(error);
  }
  async getLogs() {
    return fs.readFileSync(this.path, "utf8").split("\n").filter(Boolean).map((log) => JSON.parse(log));
  }
  async getLogsByRunId({ runId }) {
    try {
      const allLogs = await this.getLogs();
      return allLogs.filter((log) => log?.runId === runId) || [];
    } catch (error) {
      console.error("Error getting logs by runId from Upstash:", error);
      return [];
    }
  }
};

exports.FileTransport = FileTransport;
