import { InstrumentationBase, InstrumentationNodeModuleDefinition } from '@opentelemetry/instrumentation';
import { MySQL2InstrumentationConfig } from './types';
export declare class MySQL2Instrumentation extends InstrumentationBase<MySQL2InstrumentationConfig> {
    static readonly COMMON_ATTRIBUTES: {
        "db.system": string;
    };
    constructor(config?: MySQL2InstrumentationConfig);
    protected init(): InstrumentationNodeModuleDefinition[];
    private _patchQuery;
    private _patchCallbackQuery;
}
//# sourceMappingURL=instrumentation.d.ts.map