{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,0CAA0C;AAC1C,8CAA2E;AAC3E,oEAIwC;AACxC,kBAAkB;AAClB,uCAA0D;AAC1D,2CAIqB;AASrB,+BAAiC;AACjC,mCAAkC;AAKlC;;;GAGG;AACH,SAAS,qCAAqC,CAE5C,eAAkB,EAAE,QAAW;IAC/B,OAAO,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;AAClD,CAAC;AAED,MAAa,iBAAkB,SAAQ,qCAA4C;IACjF,YAAY,SAAkC,EAAE;QAC9C,KAAK,CAAC,sBAAY,EAAE,yBAAe,EAAE,MAAM,CAAC,CAAC;IAC/C,CAAC;IAED,IAAI;QAIF,OAAO;YACL,IAAI,qDAAmC,CACrC,IAAI,EACJ,CAAC,GAAG,CAAC,EACL,CAAC,EAAM,EAAE,EAAE;gBACT,KAAK,MAAM,KAAK,IAAI,0BAAc,EAAE;oBAClC,MAAM,EAAE,aAAa,EAAE,mBAAmB,EAAE,GAAG,IAAA,eAAO,EAAC,EAAE,EAAE,KAAK,CAAC,CAAC;oBAElE,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,EAAE;wBACjD,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;qBAClD;oBACD,IAAI,CAAC,KAAK,CACR,aAAa,EACb,mBAAmB,EACd,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAC/C,CAAC;iBACH;gBACD,KAAK,MAAM,KAAK,IAAI,8BAAkB,EAAE;oBACtC,MAAM,EAAE,aAAa,EAAE,mBAAmB,EAAE,GAAG,IAAA,eAAO,EAAC,EAAE,EAAE,KAAK,CAAC,CAAC;oBAClE,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,EAAE;wBACjD,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;qBAClD;oBACD,IAAI,KAAK,KAAK,QAAQ,EAAE;wBACtB,4DAA4D;wBAC5D,oEAAoE;wBACpE,IAAI,CAAC,KAAK,CACR,aAAa,EACb,mBAAmB,EACd,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CACzD,CAAC;wBACF,SAAS;qBACV;oBACD,IAAI,CAAC,KAAK,CACR,aAAa,EACb,mBAAmB,EACd,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CACnD,CAAC;iBACH;gBACD,KAAK,MAAM,KAAK,IAAI,6BAAiB,EAAE;oBACrC,IAAI,IAAA,2BAAS,EAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;wBACjC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;qBAClC;oBACD,IAAI,CAAC,KAAK,CACR,EAAE,CAAC,QAAQ,EACX,KAAK,EACA,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAClD,CAAC;iBACH;gBACD,OAAO,EAAE,CAAC;YACZ,CAAC,EACD,CAAC,EAAM,EAAE,EAAE;gBACT,IAAI,EAAE,KAAK,SAAS;oBAAE,OAAO;gBAC7B,KAAK,MAAM,KAAK,IAAI,0BAAc,EAAE;oBAClC,MAAM,EAAE,aAAa,EAAE,mBAAmB,EAAE,GAAG,IAAA,eAAO,EAAC,EAAE,EAAE,KAAK,CAAC,CAAC;oBAClE,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,EAAE;wBACjD,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;qBAClD;iBACF;gBACD,KAAK,MAAM,KAAK,IAAI,8BAAkB,EAAE;oBACtC,MAAM,EAAE,aAAa,EAAE,mBAAmB,EAAE,GAAG,IAAA,eAAO,EAAC,EAAE,EAAE,KAAK,CAAC,CAAC;oBAClE,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,EAAE;wBACjD,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;qBAClD;iBACF;gBACD,KAAK,MAAM,KAAK,IAAI,6BAAiB,EAAE;oBACrC,IAAI,IAAA,2BAAS,EAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;wBACjC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;qBAClC;iBACF;YACH,CAAC,CACF;YACD,IAAI,qDAAmC,CACrC,aAAa,EACb,CAAC,GAAG,CAAC,EACL,CAAC,UAAsB,EAAE,EAAE;gBACzB,KAAK,MAAM,KAAK,IAAI,6BAAiB,EAAE;oBACrC,IAAI,IAAA,2BAAS,EAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;wBAChC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;qBACjC;oBACD,IAAI,CAAC,KAAK,CACR,UAAU,EACV,KAAK,EACA,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAClD,CAAC;iBACH;gBACD,OAAO,UAAU,CAAC;YACpB,CAAC,EACD,CAAC,UAAsB,EAAE,EAAE;gBACzB,IAAI,UAAU,KAAK,SAAS;oBAAE,OAAO;gBACrC,KAAK,MAAM,KAAK,IAAI,6BAAiB,EAAE;oBACrC,IAAI,IAAA,2BAAS,EAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;wBAChC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;qBACjC;iBACF;YACH,CAAC,CACF;SACF,CAAC;IACJ,CAAC;IAES,kBAAkB,CAC1B,YAAqB,EACrB,QAAW;QAEX,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,MAAM,eAAe,GAAQ,UAAqB,GAAG,IAAW;YAC9D,MAAM,aAAa,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAE3C,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,aAAa,CAAC,EAAE;gBAChD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;aACnC;YACD,IACE,eAAe,CAAC,cAAc,CAAC,YAAY,EAAE;gBAC3C,IAAI,EAAE,IAAI;aACX,CAAC,KAAK,KAAK,EACZ;gBACA,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CACrB,IAAA,sBAAe,EAAC,aAAa,CAAC,EAC9B,QAAQ,EACR,IAAI,EACJ,GAAG,IAAI,CACR,CAAC;aACH;YAED,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,SAAS,CAC3C,MAAM,YAAY,EAAE,CACT,CAAC;YAEd,IAAI;gBACF,yCAAyC;gBACzC,MAAM,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAC1B,IAAA,sBAAe,EAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,EACvD,QAAQ,EACR,IAAI,EACJ,GAAG,IAAI,CACR,CAAC;gBACF,eAAe,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;gBAChE,OAAO,GAAG,CAAC;aACZ;YAAC,OAAO,KAAU,EAAE;gBACnB,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAC5B,IAAI,CAAC,SAAS,CAAC;oBACb,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,IAAI,EAAE,GAAG,CAAC,cAAc,CAAC,KAAK;iBAC/B,CAAC,CAAC;gBACH,eAAe,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;gBACvE,MAAM,KAAK,CAAC;aACb;oBAAS;gBACR,IAAI,CAAC,GAAG,EAAE,CAAC;aACZ;QACH,CAAC,CAAC;QACF,OAAO,qCAAqC,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;IAC1E,CAAC;IAES,sBAAsB,CAC9B,YAAqB,EACrB,QAAW;QAEX,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,MAAM,eAAe,GAAQ,UAAqB,GAAG,IAAW;YAC9D,MAAM,aAAa,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAE3C,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,aAAa,CAAC,EAAE;gBAChD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;aACnC;YACD,IACE,eAAe,CAAC,cAAc,CAAC,YAAY,EAAE;gBAC3C,IAAI,EAAE,IAAI;aACX,CAAC,KAAK,KAAK,EACZ;gBACA,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CACrB,IAAA,sBAAe,EAAC,aAAa,CAAC,EAC9B,QAAQ,EACR,IAAI,EACJ,GAAG,IAAI,CACR,CAAC;aACH;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;YAChC,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;YACzB,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE;gBAC5B,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,SAAS,CAC3C,MAAM,YAAY,EAAE,CACT,CAAC;gBAEd,+DAA+D;gBAC/D,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAC9B,aAAa,EACb,UAAyB,KAAa;oBACpC,IAAI,KAAK,EAAE;wBACT,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;wBAC5B,IAAI,CAAC,SAAS,CAAC;4BACb,OAAO,EAAE,KAAK,CAAC,OAAO;4BACtB,IAAI,EAAE,GAAG,CAAC,cAAc,CAAC,KAAK;yBAC/B,CAAC,CAAC;qBACJ;oBACD,eAAe,CAAC,WAAW,CAAC,YAAY,EAAE;wBACxC,IAAI,EAAE,IAAI;wBACV,IAAI;wBACJ,KAAK;qBACN,CAAC,CAAC;oBACH,IAAI,CAAC,GAAG,EAAE,CAAC;oBACX,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBACnC,CAAC,CACF,CAAC;gBAEF,IAAI;oBACF,yCAAyC;oBACzC,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CACrB,IAAA,sBAAe,EAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,EACvD,QAAQ,EACR,IAAI,EACJ,GAAG,IAAI,CACR,CAAC;iBACH;gBAAC,OAAO,KAAU,EAAE;oBACnB,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;oBAC5B,IAAI,CAAC,SAAS,CAAC;wBACb,OAAO,EAAE,KAAK,CAAC,OAAO;wBACtB,IAAI,EAAE,GAAG,CAAC,cAAc,CAAC,KAAK;qBAC/B,CAAC,CAAC;oBACH,eAAe,CAAC,WAAW,CAAC,YAAY,EAAE;wBACxC,IAAI,EAAE,IAAI;wBACV,IAAI;wBACJ,KAAK;qBACN,CAAC,CAAC;oBACH,IAAI,CAAC,GAAG,EAAE,CAAC;oBACX,MAAM,KAAK,CAAC;iBACb;aACF;iBAAM;gBACL,6DAA6D;gBAC7D,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;aACnC;QACH,CAAC,CAAC;QACF,OAAO,qCAAqC,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;IAC1E,CAAC;IAES,4BAA4B,CAEpC,YAAsB,EAAE,QAAW;QACnC,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,MAAM,eAAe,GAAQ,UAAqB,GAAG,IAAW;YAC9D,MAAM,aAAa,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAE3C,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,aAAa,CAAC,EAAE;gBAChD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;aACnC;YACD,IACE,eAAe,CAAC,cAAc,CAAC,YAAY,EAAE;gBAC3C,IAAI,EAAE,IAAI;aACX,CAAC,KAAK,KAAK,EACZ;gBACA,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CACrB,IAAA,sBAAe,EAAC,aAAa,CAAC,EAC9B,QAAQ,EACR,IAAI,EACJ,GAAG,IAAI,CACR,CAAC;aACH;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;YAChC,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;YACzB,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE;gBAC5B,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,SAAS,CAC3C,MAAM,YAAY,EAAE,CACT,CAAC;gBAEd,+DAA+D;gBAC/D,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAC9B,aAAa,EACb;oBACE,kDAAkD;oBAClD,eAAe,CAAC,WAAW,CAAC,YAAY,EAAE;wBACxC,IAAI,EAAE,IAAI;wBACV,IAAI;qBACL,CAAC,CAAC;oBACH,IAAI,CAAC,GAAG,EAAE,CAAC;oBACX,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBACnC,CAAC,CACF,CAAC;gBAEF,IAAI;oBACF,yCAAyC;oBACzC,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CACrB,IAAA,sBAAe,EAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,EACvD,QAAQ,EACR,IAAI,EACJ,GAAG,IAAI,CACR,CAAC;iBACH;gBAAC,OAAO,KAAU,EAAE;oBACnB,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;oBAC5B,IAAI,CAAC,SAAS,CAAC;wBACb,OAAO,EAAE,KAAK,CAAC,OAAO;wBACtB,IAAI,EAAE,GAAG,CAAC,cAAc,CAAC,KAAK;qBAC/B,CAAC,CAAC;oBACH,eAAe,CAAC,WAAW,CAAC,YAAY,EAAE;wBACxC,IAAI,EAAE,IAAI;wBACV,IAAI;wBACJ,KAAK;qBACN,CAAC,CAAC;oBACH,IAAI,CAAC,GAAG,EAAE,CAAC;oBACX,MAAM,KAAK,CAAC;iBACb;aACF;iBAAM;gBACL,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;aACnC;QACH,CAAC,CAAC;QACF,MAAM,8BAA8B,GAClC,qCAAqC,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;QAEnE,iFAAiF;QACjF,2CAA2C;QAC3C,MAAM,WAAW,GAAG,UAAU,IAAa;YACzC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAC3B,8BAA8B,CAAC,IAAI,EAAE,OAAO,CAAC,CAC9C,CAAC;QACJ,CAAC,CAAC;QACF,MAAM,CAAC,cAAc,CAAC,WAAW,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC;QACpE,MAAM,CAAC,cAAc,CAAC,8BAA8B,EAAE,gBAAS,CAAC,MAAM,EAAE;YACtE,KAAK,EAAE,WAAW;SACnB,CAAC,CAAC;QAEH,OAAO,8BAA8B,CAAC;IACxC,CAAC;IAES,qBAAqB,CAC7B,YAAsB,EACtB,QAAW;QAEX,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,MAAM,eAAe,GAAQ,KAAK,WAAsB,GAAG,IAAW;YACpE,MAAM,aAAa,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAE3C,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,aAAa,CAAC,EAAE;gBAChD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;aACnC;YACD,IACE,eAAe,CAAC,cAAc,CAAC,YAAY,EAAE;gBAC3C,IAAI,EAAE,IAAI;aACX,CAAC,KAAK,KAAK,EACZ;gBACA,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CACrB,IAAA,sBAAe,EAAC,aAAa,CAAC,EAC9B,QAAQ,EACR,IAAI,EACJ,GAAG,IAAI,CACR,CAAC;aACH;YAED,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,SAAS,CAC3C,MAAM,YAAY,EAAE,CACT,CAAC;YAEd,IAAI;gBACF,yCAAyC;gBACzC,MAAM,GAAG,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,IAAI,CAChC,IAAA,sBAAe,EAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,EACvD,QAAQ,EACR,IAAI,EACJ,GAAG,IAAI,CACR,CAAC;gBACF,eAAe,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;gBAChE,OAAO,GAAG,CAAC;aACZ;YAAC,OAAO,KAAU,EAAE;gBACnB,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAC5B,IAAI,CAAC,SAAS,CAAC;oBACb,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,IAAI,EAAE,GAAG,CAAC,cAAc,CAAC,KAAK;iBAC/B,CAAC,CAAC;gBACH,eAAe,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;gBACvE,MAAM,KAAK,CAAC;aACb;oBAAS;gBACR,IAAI,CAAC,GAAG,EAAE,CAAC;aACZ;QACH,CAAC,CAAC;QACF,OAAO,qCAAqC,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;IAC1E,CAAC;IAES,cAAc,CACtB,GAAG,IAA4B;QAE/B,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QACxC,IAAI,OAAO,UAAU,KAAK,UAAU,EAAE;YACpC,IAAI;gBACF,OAAO,UAAU,CAAC,GAAG,IAAI,CAAC,CAAC;aAC5B;YAAC,OAAO,CAAC,EAAE;gBACV,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC;aAChD;SACF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAES,WAAW,CAAC,GAAG,IAAyB;QAChD,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QACrC,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;YACjC,IAAI;gBACF,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC;aAClB;YAAC,OAAO,CAAC,EAAE;gBACV,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC,CAAC,CAAC;aAC7C;SACF;IACH,CAAC;IAES,YAAY,CAAC,OAAoB;QACzC,IAAI,IAAA,0BAAmB,EAAC,OAAO,CAAC,EAAE;YAChC,yEAAyE;YACzE,2DAA2D;YAC3D,OAAO,KAAK,CAAC;SACd;QAED,MAAM,EAAE,iBAAiB,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAC/C,IAAI,iBAAiB,EAAE;YACrB,MAAM,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC9C,IAAI,UAAU,IAAI,IAAI,EAAE;gBACtB,OAAO,KAAK,CAAC;aACd;SACF;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AA1aD,8CA0aC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as api from '@opentelemetry/api';\nimport { isTracingSuppressed, suppressTracing } from '@opentelemetry/core';\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  isWrapped,\n} from '@opentelemetry/instrumentation';\n/** @knipignore */\nimport { PACKAGE_NAME, PACKAGE_VERSION } from './version';\nimport {\n  CALLBACK_FUNCTIONS,\n  PROMISE_FUNCTIONS,\n  SYNC_FUNCTIONS,\n} from './constants';\nimport type * as fs from 'fs';\nimport type {\n  <PERSON><PERSON>ber,\n  FPM<PERSON>ber,\n  CreateHook,\n  EndHook,\n  FsInstrumentationConfig,\n} from './types';\nimport { promisify } from 'util';\nimport { indexFs } from './utils';\n\ntype FS = typeof fs;\ntype FSPromises = (typeof fs)['promises'];\n\n/**\n * This is important for 2-level functions like `realpath.native` to retain the 2nd-level\n * when patching the 1st-level.\n */\nfunction patchedFunctionWithOriginalProperties<\n  T extends (...args: any[]) => ReturnType<T>\n>(patchedFunction: T, original: T): T {\n  return Object.assign(patchedFunction, original);\n}\n\nexport class FsInstrumentation extends InstrumentationBase<FsInstrumentationConfig> {\n  constructor(config: FsInstrumentationConfig = {}) {\n    super(PACKAGE_NAME, PACKAGE_VERSION, config);\n  }\n\n  init(): (\n    | InstrumentationNodeModuleDefinition\n    | InstrumentationNodeModuleDefinition\n  )[] {\n    return [\n      new InstrumentationNodeModuleDefinition(\n        'fs',\n        ['*'],\n        (fs: FS) => {\n          for (const fName of SYNC_FUNCTIONS) {\n            const { objectToPatch, functionNameToPatch } = indexFs(fs, fName);\n\n            if (isWrapped(objectToPatch[functionNameToPatch])) {\n              this._unwrap(objectToPatch, functionNameToPatch);\n            }\n            this._wrap(\n              objectToPatch,\n              functionNameToPatch,\n              <any>this._patchSyncFunction.bind(this, fName)\n            );\n          }\n          for (const fName of CALLBACK_FUNCTIONS) {\n            const { objectToPatch, functionNameToPatch } = indexFs(fs, fName);\n            if (isWrapped(objectToPatch[functionNameToPatch])) {\n              this._unwrap(objectToPatch, functionNameToPatch);\n            }\n            if (fName === 'exists') {\n              // handling separately because of the inconsistent cb style:\n              // `exists` doesn't have error as the first argument, but the result\n              this._wrap(\n                objectToPatch,\n                functionNameToPatch,\n                <any>this._patchExistsCallbackFunction.bind(this, fName)\n              );\n              continue;\n            }\n            this._wrap(\n              objectToPatch,\n              functionNameToPatch,\n              <any>this._patchCallbackFunction.bind(this, fName)\n            );\n          }\n          for (const fName of PROMISE_FUNCTIONS) {\n            if (isWrapped(fs.promises[fName])) {\n              this._unwrap(fs.promises, fName);\n            }\n            this._wrap(\n              fs.promises,\n              fName,\n              <any>this._patchPromiseFunction.bind(this, fName)\n            );\n          }\n          return fs;\n        },\n        (fs: FS) => {\n          if (fs === undefined) return;\n          for (const fName of SYNC_FUNCTIONS) {\n            const { objectToPatch, functionNameToPatch } = indexFs(fs, fName);\n            if (isWrapped(objectToPatch[functionNameToPatch])) {\n              this._unwrap(objectToPatch, functionNameToPatch);\n            }\n          }\n          for (const fName of CALLBACK_FUNCTIONS) {\n            const { objectToPatch, functionNameToPatch } = indexFs(fs, fName);\n            if (isWrapped(objectToPatch[functionNameToPatch])) {\n              this._unwrap(objectToPatch, functionNameToPatch);\n            }\n          }\n          for (const fName of PROMISE_FUNCTIONS) {\n            if (isWrapped(fs.promises[fName])) {\n              this._unwrap(fs.promises, fName);\n            }\n          }\n        }\n      ),\n      new InstrumentationNodeModuleDefinition(\n        'fs/promises',\n        ['*'],\n        (fsPromises: FSPromises) => {\n          for (const fName of PROMISE_FUNCTIONS) {\n            if (isWrapped(fsPromises[fName])) {\n              this._unwrap(fsPromises, fName);\n            }\n            this._wrap(\n              fsPromises,\n              fName,\n              <any>this._patchPromiseFunction.bind(this, fName)\n            );\n          }\n          return fsPromises;\n        },\n        (fsPromises: FSPromises) => {\n          if (fsPromises === undefined) return;\n          for (const fName of PROMISE_FUNCTIONS) {\n            if (isWrapped(fsPromises[fName])) {\n              this._unwrap(fsPromises, fName);\n            }\n          }\n        }\n      ),\n    ];\n  }\n\n  protected _patchSyncFunction<T extends (...args: any[]) => ReturnType<T>>(\n    functionName: FMember,\n    original: T\n  ): T {\n    const instrumentation = this;\n    const patchedFunction = <any>function (this: any, ...args: any[]) {\n      const activeContext = api.context.active();\n\n      if (!instrumentation._shouldTrace(activeContext)) {\n        return original.apply(this, args);\n      }\n      if (\n        instrumentation._runCreateHook(functionName, {\n          args: args,\n        }) === false\n      ) {\n        return api.context.with(\n          suppressTracing(activeContext),\n          original,\n          this,\n          ...args\n        );\n      }\n\n      const span = instrumentation.tracer.startSpan(\n        `fs ${functionName}`\n      ) as api.Span;\n\n      try {\n        // Suppress tracing for internal fs calls\n        const res = api.context.with(\n          suppressTracing(api.trace.setSpan(activeContext, span)),\n          original,\n          this,\n          ...args\n        );\n        instrumentation._runEndHook(functionName, { args: args, span });\n        return res;\n      } catch (error: any) {\n        span.recordException(error);\n        span.setStatus({\n          message: error.message,\n          code: api.SpanStatusCode.ERROR,\n        });\n        instrumentation._runEndHook(functionName, { args: args, span, error });\n        throw error;\n      } finally {\n        span.end();\n      }\n    };\n    return patchedFunctionWithOriginalProperties(patchedFunction, original);\n  }\n\n  protected _patchCallbackFunction<T extends (...args: any[]) => ReturnType<T>>(\n    functionName: FMember,\n    original: T\n  ): T {\n    const instrumentation = this;\n    const patchedFunction = <any>function (this: any, ...args: any[]) {\n      const activeContext = api.context.active();\n\n      if (!instrumentation._shouldTrace(activeContext)) {\n        return original.apply(this, args);\n      }\n      if (\n        instrumentation._runCreateHook(functionName, {\n          args: args,\n        }) === false\n      ) {\n        return api.context.with(\n          suppressTracing(activeContext),\n          original,\n          this,\n          ...args\n        );\n      }\n\n      const lastIdx = args.length - 1;\n      const cb = args[lastIdx];\n      if (typeof cb === 'function') {\n        const span = instrumentation.tracer.startSpan(\n          `fs ${functionName}`\n        ) as api.Span;\n\n        // Return to the context active during the call in the callback\n        args[lastIdx] = api.context.bind(\n          activeContext,\n          function (this: unknown, error?: Error) {\n            if (error) {\n              span.recordException(error);\n              span.setStatus({\n                message: error.message,\n                code: api.SpanStatusCode.ERROR,\n              });\n            }\n            instrumentation._runEndHook(functionName, {\n              args: args,\n              span,\n              error,\n            });\n            span.end();\n            return cb.apply(this, arguments);\n          }\n        );\n\n        try {\n          // Suppress tracing for internal fs calls\n          return api.context.with(\n            suppressTracing(api.trace.setSpan(activeContext, span)),\n            original,\n            this,\n            ...args\n          );\n        } catch (error: any) {\n          span.recordException(error);\n          span.setStatus({\n            message: error.message,\n            code: api.SpanStatusCode.ERROR,\n          });\n          instrumentation._runEndHook(functionName, {\n            args: args,\n            span,\n            error,\n          });\n          span.end();\n          throw error;\n        }\n      } else {\n        // TODO: what to do if we are pretty sure it's going to throw\n        return original.apply(this, args);\n      }\n    };\n    return patchedFunctionWithOriginalProperties(patchedFunction, original);\n  }\n\n  protected _patchExistsCallbackFunction<\n    T extends (...args: any[]) => ReturnType<T>\n  >(functionName: 'exists', original: T): T {\n    const instrumentation = this;\n    const patchedFunction = <any>function (this: any, ...args: any[]) {\n      const activeContext = api.context.active();\n\n      if (!instrumentation._shouldTrace(activeContext)) {\n        return original.apply(this, args);\n      }\n      if (\n        instrumentation._runCreateHook(functionName, {\n          args: args,\n        }) === false\n      ) {\n        return api.context.with(\n          suppressTracing(activeContext),\n          original,\n          this,\n          ...args\n        );\n      }\n\n      const lastIdx = args.length - 1;\n      const cb = args[lastIdx];\n      if (typeof cb === 'function') {\n        const span = instrumentation.tracer.startSpan(\n          `fs ${functionName}`\n        ) as api.Span;\n\n        // Return to the context active during the call in the callback\n        args[lastIdx] = api.context.bind(\n          activeContext,\n          function (this: unknown) {\n            // `exists` never calls the callback with an error\n            instrumentation._runEndHook(functionName, {\n              args: args,\n              span,\n            });\n            span.end();\n            return cb.apply(this, arguments);\n          }\n        );\n\n        try {\n          // Suppress tracing for internal fs calls\n          return api.context.with(\n            suppressTracing(api.trace.setSpan(activeContext, span)),\n            original,\n            this,\n            ...args\n          );\n        } catch (error: any) {\n          span.recordException(error);\n          span.setStatus({\n            message: error.message,\n            code: api.SpanStatusCode.ERROR,\n          });\n          instrumentation._runEndHook(functionName, {\n            args: args,\n            span,\n            error,\n          });\n          span.end();\n          throw error;\n        }\n      } else {\n        return original.apply(this, args);\n      }\n    };\n    const functionWithOriginalProperties =\n      patchedFunctionWithOriginalProperties(patchedFunction, original);\n\n    // `exists` has a custom promisify function because of the inconsistent signature\n    // replicating that on the patched function\n    const promisified = function (path: unknown) {\n      return new Promise(resolve =>\n        functionWithOriginalProperties(path, resolve)\n      );\n    };\n    Object.defineProperty(promisified, 'name', { value: functionName });\n    Object.defineProperty(functionWithOriginalProperties, promisify.custom, {\n      value: promisified,\n    });\n\n    return functionWithOriginalProperties;\n  }\n\n  protected _patchPromiseFunction<T extends (...args: any[]) => ReturnType<T>>(\n    functionName: FPMember,\n    original: T\n  ): T {\n    const instrumentation = this;\n    const patchedFunction = <any>async function (this: any, ...args: any[]) {\n      const activeContext = api.context.active();\n\n      if (!instrumentation._shouldTrace(activeContext)) {\n        return original.apply(this, args);\n      }\n      if (\n        instrumentation._runCreateHook(functionName, {\n          args: args,\n        }) === false\n      ) {\n        return api.context.with(\n          suppressTracing(activeContext),\n          original,\n          this,\n          ...args\n        );\n      }\n\n      const span = instrumentation.tracer.startSpan(\n        `fs ${functionName}`\n      ) as api.Span;\n\n      try {\n        // Suppress tracing for internal fs calls\n        const res = await api.context.with(\n          suppressTracing(api.trace.setSpan(activeContext, span)),\n          original,\n          this,\n          ...args\n        );\n        instrumentation._runEndHook(functionName, { args: args, span });\n        return res;\n      } catch (error: any) {\n        span.recordException(error);\n        span.setStatus({\n          message: error.message,\n          code: api.SpanStatusCode.ERROR,\n        });\n        instrumentation._runEndHook(functionName, { args: args, span, error });\n        throw error;\n      } finally {\n        span.end();\n      }\n    };\n    return patchedFunctionWithOriginalProperties(patchedFunction, original);\n  }\n\n  protected _runCreateHook(\n    ...args: Parameters<CreateHook>\n  ): ReturnType<CreateHook> {\n    const { createHook } = this.getConfig();\n    if (typeof createHook === 'function') {\n      try {\n        return createHook(...args);\n      } catch (e) {\n        this._diag.error('caught createHook error', e);\n      }\n    }\n    return true;\n  }\n\n  protected _runEndHook(...args: Parameters<EndHook>): ReturnType<EndHook> {\n    const { endHook } = this.getConfig();\n    if (typeof endHook === 'function') {\n      try {\n        endHook(...args);\n      } catch (e) {\n        this._diag.error('caught endHook error', e);\n      }\n    }\n  }\n\n  protected _shouldTrace(context: api.Context): boolean {\n    if (isTracingSuppressed(context)) {\n      // Performance optimization. Avoid creating additional contexts and spans\n      // if we already know that the tracing is being suppressed.\n      return false;\n    }\n\n    const { requireParentSpan } = this.getConfig();\n    if (requireParentSpan) {\n      const parentSpan = api.trace.getSpan(context);\n      if (parentSpan == null) {\n        return false;\n      }\n    }\n\n    return true;\n  }\n}\n"]}