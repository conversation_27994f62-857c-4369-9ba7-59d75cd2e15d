{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { InstrumentationConfig } from '@opentelemetry/instrumentation';\nimport { Span } from '@opentelemetry/api';\n\nexport type CommandArgs = Array<string | Buffer | number | any[]>;\n\n/**\n * Function that can be used to serialize db.statement tag\n * @param cmdName - The name of the command (eg. set, get, mset)\n * @param cmdArgs - Array of arguments passed to the command\n *\n * @returns serialized string that will be used as the db.statement attribute.\n */\nexport type DbStatementSerializer = (\n  cmdName: string,\n  cmdArgs: CommandArgs\n) => string;\n\nexport interface IORedisRequestHookInformation {\n  moduleVersion?: string;\n  cmdName: string;\n  cmdArgs: CommandArgs;\n}\n\nexport interface RedisRequestCustomAttributeFunction {\n  (span: Span, requestInfo: IORedisRequestHookInformation): void;\n}\n\n/**\n * Function that can be used to add custom attributes to span on response from redis server\n * @param span - The span created for the redis command, on which attributes can be set\n * @param cmdName - The name of the command (eg. set, get, mset)\n * @param cmdArgs - Array of arguments passed to the command\n * @param response - The response object which is returned to the user who called this command.\n *  Can be used to set custom attributes on the span.\n *  The type of the response varies depending on the specific command.\n */\nexport interface RedisResponseCustomAttributeFunction {\n  (span: Span, cmdName: string, cmdArgs: CommandArgs, response: unknown): void;\n}\n\n/**\n * Options available for the IORedis Instrumentation (see [documentation](https://github.com/open-telemetry/opentelemetry-js-contrib/blob/main/plugins/node/opentelemetry-instrumentation-ioredis/README.md#ioredis-instrumentation-options))\n */\nexport interface IORedisInstrumentationConfig extends InstrumentationConfig {\n  /** Custom serializer function for the db.statement tag */\n  dbStatementSerializer?: DbStatementSerializer;\n\n  /** Function for adding custom attributes on db request */\n  requestHook?: RedisRequestCustomAttributeFunction;\n\n  /** Function for adding custom attributes on db response */\n  responseHook?: RedisResponseCustomAttributeFunction;\n\n  /** Require parent to create ioredis span, default when unset is true */\n  requireParentSpan?: boolean;\n}\n"]}