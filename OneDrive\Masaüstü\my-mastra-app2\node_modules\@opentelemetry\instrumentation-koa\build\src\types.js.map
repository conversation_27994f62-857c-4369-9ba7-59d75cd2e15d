{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";;;AAkBA,IAAY,YAGX;AAHD,WAAY,YAAY;IACtB,iCAAiB,CAAA;IACjB,yCAAyB,CAAA;AAC3B,CAAC,EAHW,YAAY,GAAZ,oBAAY,KAAZ,oBAAY,QAGvB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { Span } from '@opentelemetry/api';\nimport { InstrumentationConfig } from '@opentelemetry/instrumentation';\n\nexport enum KoaLayerType {\n  ROUTER = 'router',\n  MIDDLEWARE = 'middleware',\n}\n\n/**\n * Information about the current Koa middleware layer\n * The middleware layer type is any by default.\n * One can install koa types packages `@types/koa` and `@types/koa__router`\n * with compatible versions to the koa version used in the project\n * to get more specific types for the middleware layer property.\n *\n * Example use in a custom attribute function:\n * ```ts\n * import type { Middleware, ParameterizedContext, DefaultState } from 'koa';\n * import type { RouterParamContext } from '@koa/router';\n *\n * type KoaContext = ParameterizedContext<DefaultState, RouterParamContext>;\n * type KoaMiddleware = Middleware<DefaultState, KoaContext>;\n *\n * const koaConfig: KoaInstrumentationConfig<KoaContext, KoaMiddleware> = {\n *  requestHook: (span: Span, info: KoaRequestInfo<KoaContext, KoaMiddleware>) => {\n *   // custom typescript code that can access the typed into.middlewareLayer and info.context\n * }\n *\n */\nexport type KoaRequestInfo<KoaContextType = any, KoaMiddlewareType = any> = {\n  context: KoaContextType;\n  middlewareLayer: KoaMiddlewareType;\n  layerType: KoaLayerType;\n};\n\n/**\n * Function that can be used to add custom attributes to the current span\n * @param span - The Express middleware layer span.\n * @param context - The current KoaContext.\n */\nexport interface KoaRequestCustomAttributeFunction<\n  KoaContextType = any,\n  KoaMiddlewareType = any\n> {\n  (span: Span, info: KoaRequestInfo<KoaContextType, KoaMiddlewareType>): void;\n}\n\n/**\n * Options available for the Koa Instrumentation (see [documentation](https://github.com/open-telemetry/opentelemetry-js/tree/main/packages/opentelemetry-Instrumentation-koa#koa-Instrumentation-options))\n */\nexport interface KoaInstrumentationConfig<\n  KoaContextType = any,\n  KoaMiddlewareType = any\n> extends InstrumentationConfig {\n  /** Ignore specific layers based on their type */\n  ignoreLayersType?: KoaLayerType[];\n  /** Function for adding custom attributes to each middleware layer span */\n  requestHook?: KoaRequestCustomAttributeFunction<\n    KoaContextType,\n    KoaMiddlewareType\n  >;\n}\n"]}