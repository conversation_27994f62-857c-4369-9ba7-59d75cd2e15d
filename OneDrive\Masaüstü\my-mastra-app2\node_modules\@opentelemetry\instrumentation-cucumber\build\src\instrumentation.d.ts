import { InstrumentationBase, InstrumentationNodeModuleDefinition } from '@opentelemetry/instrumentation';
import { CucumberInstrumentationConfig } from './types';
export declare class CucumberInstrumentation extends InstrumentationBase<CucumberInstrumentationConfig> {
    private module;
    constructor(config?: CucumberInstrumentationConfig);
    init(): InstrumentationNodeModuleDefinition[];
    private static mapTags;
    private static setSpanToError;
    private setSpanToStepStatus;
    private _getTestCaseRunPatch;
    private _getTestCaseRunStepPatch;
    private _getTestCaseRunAttemptPatch;
    private _getHookPatch;
    private _getStepPatch;
}
//# sourceMappingURL=instrumentation.d.ts.map