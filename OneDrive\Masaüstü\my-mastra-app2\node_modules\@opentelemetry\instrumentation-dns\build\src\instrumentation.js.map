{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,4CAA0D;AAC1D,oEAKwC;AAGxC,iCAAiC;AACjC,kBAAkB;AAClB,uCAA0D;AAM1D;;GAEG;AACH,MAAa,kBAAmB,SAAQ,qCAA6C;IACnF,YAAY,SAAmC,EAAE;QAC/C,KAAK,CAAC,sBAAY,EAAE,yBAAe,EAAE,MAAM,CAAC,CAAC;IAC/C,CAAC;IAED,IAAI;QAIF,OAAO;YACL,IAAI,qDAAmC,CACrC,KAAK,EACL,CAAC,GAAG,CAAC,EACL,CAAC,aAAyB,EAAE,EAAE;gBAC5B,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,MAAM,CAAC,EAAE;oBACnC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;iBACvC;gBACD,8DAA8D;gBAC9D,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,CAAC,UAAU,EAAS,CAAC,CAAC;gBAC9D,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,QAAQ,EACtB,QAAQ;gBACR,8DAA8D;gBAC9D,IAAI,CAAC,UAAU,EAAS,CACzB,CAAC;gBACF,OAAO,aAAa,CAAC;YACvB,CAAC,EACD,aAAa,CAAC,EAAE;gBACd,IAAI,aAAa,KAAK,SAAS;oBAAE,OAAO;gBACxC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;gBACtC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACjD,CAAC,CACF;YACD,IAAI,qDAAmC,CACrC,cAAc,EACd,CAAC,GAAG,CAAC,EACL,CAAC,aAAiC,EAAE,EAAE;gBACpC,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,MAAM,CAAC,EAAE;oBACnC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;iBACvC;gBACD,8DAA8D;gBAC9D,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,CAAC,UAAU,EAAS,CAAC,CAAC;gBAC9D,OAAO,aAAa,CAAC;YACvB,CAAC,EACD,aAAa,CAAC,EAAE;gBACd,IAAI,aAAa,KAAK,SAAS;oBAAE,OAAO;gBACxC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;YACxC,CAAC,CACF;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,UAAU;QAChB,OAAO,CAAC,QAAwD,EAAE,EAAE;YAClE,OAAO,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;QAChD,CAAC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,uBAAuB,CAC7B,QAAwD;QAExD,MAAM,MAAM,GAAG,IAAI,CAAC;QACpB,OAAO,SAAS,aAAa,CAE3B,QAAgB,EAChB,GAAG,IAAe;YAElB,IACE,KAAK,CAAC,SAAS,CACb,QAAQ,EACR,MAAM,CAAC,SAAS,EAAE,CAAC,eAAe,EAClC,CAAC,CAAQ,EAAE,EAAE,CAAC,UAAI,CAAC,KAAK,CAAC,+BAA+B,EAAE,CAAC,CAAC,CAC7D,EACD;gBACA,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;aAClD;YAED,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;YAC9B,UAAI,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;YAC5D,MAAM,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAC9C,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE;gBACzC,IAAI,EAAE,cAAQ,CAAC,MAAM;aACtB,CAAC,CAAC;YAEH,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;YAC7C,IAAI,OAAO,gBAAgB,KAAK,UAAU,EAAE;gBAC1C,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,mBAAmB,CAC9C,gBAAgB,EAChB,IAAI,CACL,CAAC;gBACF,OAAO,IAAA,wCAAsB,EAC3B,GAAG,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,CAAC,EAC/C,KAAK,CAAC,EAAE;oBACN,IAAI,KAAK,IAAI,IAAI,EAAE;wBACjB,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;wBAC5B,IAAI,CAAC,GAAG,EAAE,CAAC;qBACZ;gBACH,CAAC,CACF,CAAC;aACH;iBAAM;gBACL,MAAM,OAAO,GAAG,IAAA,wCAAsB,EACpC,GAAG,EAAE,CACF,QAAmC,CAAC,KAAK,CAAC,IAAI,EAAE;oBAC/C,QAAQ;oBACR,GAAG,IAAI;iBACR,CAAC,EACJ,KAAK,CAAC,EAAE;oBACN,IAAI,KAAK,IAAI,IAAI,EAAE;wBACjB,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;wBAC5B,IAAI,CAAC,GAAG,EAAE,CAAC;qBACZ;gBACH,CAAC,CACF,CAAC;gBACF,OAAO,CAAC,IAAI,CACV,MAAM,CAAC,EAAE;oBACP,KAAK,CAAC,mBAAmB,CAAC,IAAI,EAAE,MAAuB,CAAC,CAAC;oBACzD,IAAI,CAAC,GAAG,EAAE,CAAC;gBACb,CAAC,EACD,CAAC,CAAwB,EAAE,EAAE;oBAC3B,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;oBACxB,IAAI,CAAC,GAAG,EAAE,CAAC;gBACb,CAAC,CACF,CAAC;gBAEF,OAAO,OAAO,CAAC;aAChB;QACH,CAAC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,mBAAmB,CACzB,QAAkB,EAClB,IAAU;QAEV,OAAO,SAAS,qBAAqB,CAEnC,GAAiC,EACjC,OAAiC,EACjC,MAAsB;YAEtB,UAAI,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;YAEzD,IAAI,GAAG,KAAK,IAAI,EAAE;gBAChB,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;aAC3B;iBAAM;gBACL,KAAK,CAAC,mBAAmB,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;aAClD;YAED,IAAI,CAAC,GAAG,EAAE,CAAC;YACX,UAAI,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;YAC1D,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACzC,CAAC,CAAC;IACJ,CAAC;CACF;AAjKD,gDAiKC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LookupAddress } from 'dns';\nimport type * as dns from 'dns';\nimport * as dnsPromises from 'dns/promises';\nimport { diag, Span, SpanKind } from '@opentelemetry/api';\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  isWrapped,\n  safeExecuteInTheMiddle,\n} from '@opentelemetry/instrumentation';\nimport { AddressFamily } from './enums/AddressFamily';\nimport { DnsInstrumentationConfig } from './types';\nimport * as utils from './utils';\n/** @knipignore */\nimport { PACKAGE_NAME, PACKAGE_VERSION } from './version';\nimport {\n  LookupCallbackSignature,\n  LookupPromiseSignature,\n} from './internal-types';\n\n/**\n * Dns instrumentation for Opentelemetry\n */\nexport class DnsInstrumentation extends InstrumentationBase<DnsInstrumentationConfig> {\n  constructor(config: DnsInstrumentationConfig = {}) {\n    super(PACKAGE_NAME, PACKAGE_VERSION, config);\n  }\n\n  init(): (\n    | InstrumentationNodeModuleDefinition\n    | InstrumentationNodeModuleDefinition\n  )[] {\n    return [\n      new InstrumentationNodeModuleDefinition(\n        'dns',\n        ['*'],\n        (moduleExports: typeof dns) => {\n          if (isWrapped(moduleExports.lookup)) {\n            this._unwrap(moduleExports, 'lookup');\n          }\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          this._wrap(moduleExports, 'lookup', this._getLookup() as any);\n          this._wrap(\n            moduleExports.promises,\n            'lookup',\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            this._getLookup() as any\n          );\n          return moduleExports;\n        },\n        moduleExports => {\n          if (moduleExports === undefined) return;\n          this._unwrap(moduleExports, 'lookup');\n          this._unwrap(moduleExports.promises, 'lookup');\n        }\n      ),\n      new InstrumentationNodeModuleDefinition(\n        'dns/promises',\n        ['*'],\n        (moduleExports: typeof dnsPromises) => {\n          if (isWrapped(moduleExports.lookup)) {\n            this._unwrap(moduleExports, 'lookup');\n          }\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          this._wrap(moduleExports, 'lookup', this._getLookup() as any);\n          return moduleExports;\n        },\n        moduleExports => {\n          if (moduleExports === undefined) return;\n          this._unwrap(moduleExports, 'lookup');\n        }\n      ),\n    ];\n  }\n\n  /**\n   * Get the patched lookup function\n   */\n  private _getLookup() {\n    return (original: (hostname: string, ...args: unknown[]) => void) => {\n      return this._getPatchLookupFunction(original);\n    };\n  }\n\n  /**\n   * Creates spans for lookup operations, restoring spans' context if applied.\n   */\n  private _getPatchLookupFunction(\n    original: (hostname: string, ...args: unknown[]) => void\n  ) {\n    const plugin = this;\n    return function patchedLookup(\n      this: {},\n      hostname: string,\n      ...args: unknown[]\n    ) {\n      if (\n        utils.isIgnored(\n          hostname,\n          plugin.getConfig().ignoreHostnames,\n          (e: Error) => diag.error('caught ignoreHostname error: ', e)\n        )\n      ) {\n        return original.apply(this, [hostname, ...args]);\n      }\n\n      const argsCount = args.length;\n      diag.debug('wrap lookup callback function and starts span');\n      const name = utils.getOperationName('lookup');\n      const span = plugin.tracer.startSpan(name, {\n        kind: SpanKind.CLIENT,\n      });\n\n      const originalCallback = args[argsCount - 1];\n      if (typeof originalCallback === 'function') {\n        args[argsCount - 1] = plugin._wrapLookupCallback(\n          originalCallback,\n          span\n        );\n        return safeExecuteInTheMiddle(\n          () => original.apply(this, [hostname, ...args]),\n          error => {\n            if (error != null) {\n              utils.setError(error, span);\n              span.end();\n            }\n          }\n        );\n      } else {\n        const promise = safeExecuteInTheMiddle(\n          () =>\n            (original as LookupPromiseSignature).apply(this, [\n              hostname,\n              ...args,\n            ]),\n          error => {\n            if (error != null) {\n              utils.setError(error, span);\n              span.end();\n            }\n          }\n        );\n        promise.then(\n          result => {\n            utils.setLookupAttributes(span, result as LookupAddress);\n            span.end();\n          },\n          (e: NodeJS.ErrnoException) => {\n            utils.setError(e, span);\n            span.end();\n          }\n        );\n\n        return promise;\n      }\n    };\n  }\n\n  /**\n   * Wrap lookup callback function\n   */\n  private _wrapLookupCallback(\n    original: Function,\n    span: Span\n  ): LookupCallbackSignature {\n    return function wrappedLookupCallback(\n      this: {},\n      err: NodeJS.ErrnoException | null,\n      address: string | LookupAddress[],\n      family?: AddressFamily\n    ): void {\n      diag.debug('executing wrapped lookup callback function');\n\n      if (err !== null) {\n        utils.setError(err, span);\n      } else {\n        utils.setLookupAttributes(span, address, family);\n      }\n\n      span.end();\n      diag.debug('executing original lookup callback function');\n      return original.apply(this, arguments);\n    };\n  }\n}\n"]}