{"name": "promise-limit", "version": "2.7.0", "description": "limits calls to functions that return promises", "main": "index.js", "dependencies": {}, "devDependencies": {"chai": "4.1.2", "chai-as-promised": "7.1.1", "eslint": "*", "eslint-config-standard": "11.0.0", "eslint-plugin-es5": "1.3.1", "eslint-plugin-import": "*", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "3.8.0", "eslint-plugin-standard": "3.1.0", "fs-promise": "2.0.3", "lowscore": "1.17.0", "mocha": "*"}, "scripts": {"test": "mocha && eslint ."}, "repository": {"type": "git", "url": "git+https://github.com/featurist/promise-limit.git"}, "keywords": ["limit", "promise", "semaphore"], "author": "<PERSON> <timma<PERSON><PERSON><PERSON>@gmail.com>", "license": "ISC", "bugs": {"url": "https://github.com/featurist/promise-limit/issues"}, "homepage": "https://github.com/featurist/promise-limit#readme"}