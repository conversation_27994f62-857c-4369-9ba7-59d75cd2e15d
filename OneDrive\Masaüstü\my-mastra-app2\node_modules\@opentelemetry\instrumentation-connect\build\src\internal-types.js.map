{"version": 3, "file": "internal-types.js", "sourceRoot": "", "sources": ["../../src/internal-types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAIU,QAAA,sBAAsB,GAAkB,MAAM,CACzD,2DAA2D,CAC5D,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { HandleFunction, IncomingMessage, Server } from 'connect';\n\nexport const _LAYERS_STORE_PROPERTY: unique symbol = Symbol(\n  'opentelemetry.instrumentation-connect.request-route-stack'\n);\n\nexport type UseArgs1 = [HandleFunction];\nexport type UseArgs2 = [string, HandleFunction];\nexport type UseArgs = UseArgs1 | UseArgs2;\nexport type Use = (...args: UseArgs) => Server;\nexport type PatchedRequest = {\n  [_LAYERS_STORE_PROPERTY]: string[];\n} & IncomingMessage;\n"]}