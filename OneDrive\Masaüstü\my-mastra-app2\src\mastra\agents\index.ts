import { Agent } from '@mastra/core';
import { openai } from '@ai-sdk/openai';

// Şimdilik MCP client olmadan agent oluşturalım ve manual tool ekleyelim
console.log('🚀 Agent oluşturuluyor...');

// Manual tool tanımla
const getRandomJokeTool = {
  name: 'get_random_joke',
  description: 'Rastgele bir şaka getirir',
  parameters: {
    type: 'object',
    properties: {},
    required: []
  },
  execute: async () => {
    try {
      console.log('🎭 get_random_joke tool çağrılıyor...');

      // Önce gerçek bir şaka API'si deneyelim (JokeAPI)
      try {
        console.log('🔄 JokeAPI deneniyor...');
        const jokeResponse = await fetch('https://v2.jokeapi.dev/joke/Programming?blacklistFlags=nsfw,religious,political,racist,sexist,explicit&type=single');

        if (jokeResponse.ok) {
          const jokeData = await jokeResponse.json();
          if (jokeData.joke) {
            console.log('✅ JokeAPI\'den şaka alındı:', jokeData.joke);
            return `🎭 İşte size bir programlama şakası: ${jokeData.joke}`;
          }
        }
      } catch (jokeError) {
        console.log('⚠️ JokeAPI başarısız, Smithery deneniyor...');
      }

      // Şimdi Smithery MCP API'sini dene
      try {
        console.log('🔄 Smithery MCP API deneniyor...');

        // Smithery'nin doğru endpoint formatını dene
        const mcpResponse = await fetch('https://server.smithery.ai/@meren41/s-navmobil/mcp?api_key=02050232-0fc4-4ada-91d7-aa175ae658a7', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify({
            jsonrpc: '2.0',
            id: 1,
            method: 'tools/call',
            params: {
              name: 'get_random_joke',
              arguments: {}
            }
          })
        });

        if (mcpResponse.ok) {
          const mcpData = await mcpResponse.json();
          console.log('🎭 Smithery MCP yanıtı:', JSON.stringify(mcpData, null, 2));

          // MCP response formatını kontrol et
          if (mcpData.result) {
            if (mcpData.result.content) {
              if (Array.isArray(mcpData.result.content)) {
                const textContent = mcpData.result.content.find((c: any) => c.type === 'text');
                if (textContent) {
                  console.log('✅ Smithery\'den şaka alındı:', textContent.text);
                  return `🎭 Smithery MCP: ${textContent.text}`;
                }
              }
              if (typeof mcpData.result.content === 'string') {
                console.log('✅ Smithery\'den şaka alındı:', mcpData.result.content);
                return `🎭 Smithery MCP: ${mcpData.result.content}`;
              }
            }
            if (typeof mcpData.result === 'string') {
              console.log('✅ Smithery\'den şaka alındı:', mcpData.result);
              return `🎭 Smithery MCP: ${mcpData.result}`;
            }
          }
        } else {
          console.log(`⚠️ Smithery MCP hatası: ${mcpResponse.status} ${mcpResponse.statusText}`);
        }
      } catch (mcpError) {
        console.log('⚠️ Smithery MCP başarısız:', mcpError);
      }

      // Fallback şakalar
      const fallbackJokes = [
        'Neden bilgisayarlar soğuk algınlığına yakalanmaz? Çünkü Windows açık bırakmazlar! 😄',
        'Programcı neden merdiven çıkmaz? Çünkü her zaman elevator kullanır! 😄',
        'Bug nedir? Feature\'ın henüz dokümante edilmemiş hali! 🐛',
        'Neden programcılar karanlıkta çalışmayı sever? Çünkü light theme göz yakar! 💡',
        'HTML bir programlama dili midir? Bu soruyu sormak bile bir bug! 😅'
      ];

      const randomJoke = fallbackJokes[Math.floor(Math.random() * fallbackJokes.length)];
      console.log('✅ Fallback şaka kullanıldı:', randomJoke);
      return `🎭 İşte size bir şaka: ${randomJoke}`;

    } catch (error) {
      console.error('❌ Tool hatası:', error);
      return '🎭 İşte size bir şaka: Programcının en büyük korkusu nedir? Ctrl+Z\'nin çalışmaması! �';
    }
  }
};

// Agent'ı oluştur
export const myAgent = new Agent({
  name: 'MCP Chat Agent',
  instructions: `Sen bir şaka robotu asistanısın. Her mesajda MUTLAKA get_random_joke tool'unu kullanmalısın.

  ZORUNLU KURALLAR:
  1. HER MESAJDA get_random_joke tool'unu çağır - İSTİSNA YOK!
  2. Önce tool'u çağır, sonra yanıt ver
  3. Kullanıcı ne derse desin, her zaman bir şaka getir
  4. Tool çağırmadan asla yanıt verme

  TOOL KULLANIMI:
  - Tool adı: get_random_joke
  - Parametre: {} (boş obje)
  - Her mesajda MUTLAKA kullan

  YANIT FORMATI:
  1. get_random_joke tool'unu çağır
  2. Şakayı kullanıcıya sun
  3. Kullanıcının sorusunu yanıtla

  UNUTMA: HER MESAJDA TOOL KULLAN!`,
  model: openai('gpt-4o-mini'),
  tools: [getRandomJokeTool],
});
