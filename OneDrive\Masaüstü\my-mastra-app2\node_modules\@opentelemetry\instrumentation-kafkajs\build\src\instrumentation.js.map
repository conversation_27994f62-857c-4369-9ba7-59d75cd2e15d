{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAa4B;AAC5B,oEAKwC;AACxC,8EAK6C;AAY7C,qDAAuD;AACvD,6CAAmD;AACnD,uCAkBmB;AAEnB,kBAAkB;AAClB,uCAA0D;AA2D1D,SAAS,cAAc,CACrB,KAAiB,EACjB,KAAa,EACb,UAAa;IAEb,OAAO,CAAC,SAA8B,EAAE,EAAE;QACxC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE;YACf,GAAG,UAAU;YACb,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,sCAAe,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;SACvD,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,wBAAwB,CAC/B,KAAmB,EACnB,KAAa,EACb,UAAa;IAEb,OAAO,CAAC,SAA8B,EAAE,EAAE;QACxC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,GAAG,IAAI,EAAE;YACxC,GAAG,UAAU;YACb,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,sCAAe,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;SACvD,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,2BAA2B,GAAG;IAClC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE;CAC1E,CAAC;AACF,MAAa,sBAAuB,SAAQ,qCAAiD;IAM3F,YAAY,SAAuC,EAAE;QACnD,KAAK,CAAC,sBAAY,EAAE,yBAAe,EAAE,MAAM,CAAC,CAAC;IAC/C,CAAC;IAEQ,wBAAwB;QAC/B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAC/C,oDAA0C,EAC1C,EAAE,MAAM,EAAE,EAAE,wBAAwB,EAAE,2BAA2B,EAAE,EAAE,CACtE,CAAC;QACF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAC3C,+CAAqC,CACtC,CAAC;QACF,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAC/C,mDAAyC,CAC1C,CAAC;QACF,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAChD,2CAAiC,EACjC,EAAE,MAAM,EAAE,EAAE,wBAAwB,EAAE,2BAA2B,EAAE,EAAE,CACtE,CAAC;IACJ,CAAC;IAES,IAAI;QACZ,MAAM,OAAO,GAAG,CAAC,aAA6B,EAAE,EAAE;YAChD,IAAI,IAAA,2BAAS,EAAC,aAAa,EAAE,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,EAAE;gBACvD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;aACzD;YACD,IAAI,IAAA,2BAAS,EAAC,aAAa,EAAE,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,EAAE;gBACvD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;aACzD;QACH,CAAC,CAAC;QAEF,MAAM,MAAM,GAAG,IAAI,qDAAmC,CACpD,SAAS,EACT,CAAC,YAAY,CAAC,EACd,CAAC,aAA6B,EAAE,EAAE;YAChC,OAAO,CAAC,aAAa,CAAC,CAAC;YACvB,IAAI,CAAC,KAAK,CACR,aAAa,EAAE,KAAK,EAAE,SAAS,EAC/B,UAAU,EACV,IAAI,CAAC,iBAAiB,EAAE,CACzB,CAAC;YACF,IAAI,CAAC,KAAK,CACR,aAAa,EAAE,KAAK,EAAE,SAAS,EAC/B,UAAU,EACV,IAAI,CAAC,iBAAiB,EAAE,CACzB,CAAC;YAEF,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,OAAO,CACR,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,iBAAiB;QACvB,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,CAAC,QAAmC,EAAE,EAAE;YAC7C,OAAO,SAAS,QAAQ,CAEtB,GAAG,IAA2C;gBAE9C,MAAM,WAAW,GAAa,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAEzD,IAAI,IAAA,2BAAS,EAAC,WAAW,CAAC,GAAG,CAAC,EAAE;oBAC9B,eAAe,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;iBAC7C;gBAED,eAAe,CAAC,KAAK,CACnB,WAAW,EACX,KAAK,EACL,eAAe,CAAC,oBAAoB,EAAE,CACvC,CAAC;gBAEF,eAAe,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;gBAErD,OAAO,WAAW,CAAC;YACrB,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,uBAAuB,CAAC,QAA2B;QACzD,IAAI,QAAQ,CAAC,oCAAmB,CAAC;YAAE,OAAO;QAE1C,yDAAyD;QACzD,IAAI,QAAQ,CAAC,MAAM,EAAE,OAAO,EAAE;YAC5B,QAAQ,CAAC,EAAE,CACT,QAAQ,CAAC,MAAM,CAAC,OAAO,EACvB,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,CAC5C,CAAC;SACH;QAED,QAAQ,CAAC,oCAAmB,CAAC,GAAG,IAAI,CAAC;IACvC,CAAC;IAEO,2BAA2B,CACjC,KAA4C;QAE5C,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACxD,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,EAAE;YACzD,CAAC,+BAAqB,CAAC,EAAE,sCAA4B;YACrD,CAAC,uCAA6B,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE;YAC3D,CAAC,0CAAmB,CAAC,EAAE,OAAO;YAC9B,CAAC,uCAAgB,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC;SAC9C,CAAC,CAAC;IACL,CAAC;IAEO,iBAAiB;QACvB,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,CAAC,QAAmC,EAAE,EAAE;YAC7C,OAAO,SAAS,QAAQ,CAEtB,GAAG,IAA2C;gBAE9C,MAAM,WAAW,GAAa,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAEzD,IAAI,IAAA,2BAAS,EAAC,WAAW,CAAC,SAAS,CAAC,EAAE;oBACpC,eAAe,CAAC,OAAO,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;iBACnD;gBACD,eAAe,CAAC,KAAK,CACnB,WAAW,EACX,WAAW,EACX,eAAe,CAAC,0BAA0B,EAAE,CAC7C,CAAC;gBAEF,IAAI,IAAA,2BAAS,EAAC,WAAW,CAAC,IAAI,CAAC,EAAE;oBAC/B,eAAe,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;iBAC9C;gBACD,eAAe,CAAC,KAAK,CACnB,WAAW,EACX,MAAM,EACN,eAAe,CAAC,qBAAqB,EAAE,CACxC,CAAC;gBAEF,eAAe,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;gBAErD,OAAO,WAAW,CAAC;YACrB,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,oBAAoB;QAC1B,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,CAAC,QAAyB,EAAE,EAAE;YACnC,OAAO,SAAS,GAAG,CAEjB,GAAG,IAAiC;gBAEpC,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACvB,IAAI,MAAM,EAAE,WAAW,EAAE;oBACvB,IAAI,IAAA,2BAAS,EAAC,MAAM,CAAC,WAAW,CAAC,EAAE;wBACjC,eAAe,CAAC,OAAO,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;qBAChD;oBACD,eAAe,CAAC,KAAK,CACnB,MAAM,EACN,aAAa,EACb,eAAe,CAAC,4BAA4B,EAAE,CAC/C,CAAC;iBACH;gBACD,IAAI,MAAM,EAAE,SAAS,EAAE;oBACrB,IAAI,IAAA,2BAAS,EAAC,MAAM,CAAC,SAAS,CAAC,EAAE;wBAC/B,eAAe,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;qBAC9C;oBACD,eAAe,CAAC,KAAK,CACnB,MAAM,EACN,WAAW,EACX,eAAe,CAAC,0BAA0B,EAAE,CAC7C,CAAC;iBACH;gBACD,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YACrC,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,4BAA4B;QAClC,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,CAAC,QAA0C,EAAE,EAAE;YACpD,OAAO,SAAS,WAAW,CAEzB,GAAG,IAAoC;gBAEvC,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACxB,MAAM,iBAAiB,GAAY,iBAAW,CAAC,OAAO,CACpD,kBAAY,EACZ,OAAO,CAAC,OAAO,CAAC,OAAO,EACvB,gCAAmB,CACpB,CAAC;gBACF,MAAM,IAAI,GAAG,eAAe,CAAC,kBAAkB,CAAC;oBAC9C,KAAK,EAAE,OAAO,CAAC,KAAK;oBACpB,OAAO,EAAE,OAAO,CAAC,OAAO;oBACxB,aAAa,EAAE,gDAAsC;oBACrD,GAAG,EAAE,iBAAiB;oBACtB,UAAU,EAAE;wBACV,CAAC,iDAAuC,CAAC,EAAE,MAAM,CAC/C,OAAO,CAAC,SAAS,CAClB;qBACF;iBACF,CAAC,CAAC;gBAEH,MAAM,cAAc,GAA0B;oBAC5C,wBAAwB,CACtB,eAAe,CAAC,gBAAgB,EAChC,IAAI,CAAC,GAAG,EAAE,EACV;wBACE,CAAC,+BAAqB,CAAC,EAAE,sCAA4B;wBACrD,CAAC,uCAA6B,CAAC,EAAE,SAAS;wBAC1C,CAAC,yCAA+B,CAAC,EAAE,OAAO,CAAC,KAAK;wBAChD,CAAC,iDAAuC,CAAC,EAAE,MAAM,CAC/C,OAAO,CAAC,SAAS,CAClB;qBACF,CACF;oBACD,cAAc,CAAC,eAAe,CAAC,iBAAiB,EAAE,CAAC,EAAE;wBACnD,CAAC,+BAAqB,CAAC,EAAE,sCAA4B;wBACrD,CAAC,uCAA6B,CAAC,EAAE,SAAS;wBAC1C,CAAC,yCAA+B,CAAC,EAAE,OAAO,CAAC,KAAK;wBAChD,CAAC,iDAAuC,CAAC,EAAE,MAAM,CAC/C,OAAO,CAAC,SAAS,CAClB;qBACF,CAAC;iBACH,CAAC;gBAEF,MAAM,kBAAkB,GAAG,aAAO,CAAC,IAAI,CACrC,WAAK,CAAC,OAAO,CAAC,iBAAiB,EAAE,IAAI,CAAC,EACtC,GAAG,EAAE;oBACH,OAAO,QAAS,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACrC,CAAC,CACF,CAAC;gBACF,OAAO,eAAe,CAAC,kBAAkB,CACvC,CAAC,IAAI,CAAC,EACN,cAAc,EACd,kBAAkB,CACnB,CAAC;YACJ,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,0BAA0B;QAChC,OAAO,CAAC,QAAwC,EAAE,EAAE;YAClD,MAAM,eAAe,GAAG,IAAI,CAAC;YAC7B,OAAO,SAAS,SAAS,CAEvB,GAAG,IAAkC;gBAErC,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACxB,gKAAgK;gBAChK,MAAM,aAAa,GAAG,eAAe,CAAC,kBAAkB,CAAC;oBACvD,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK;oBAC1B,OAAO,EAAE,SAAS;oBAClB,aAAa,EAAE,gDAAsC;oBACrD,GAAG,EAAE,kBAAY;oBACjB,UAAU,EAAE;wBACV,CAAC,4CAAkC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM;wBACnE,CAAC,iDAAuC,CAAC,EAAE,MAAM,CAC/C,OAAO,CAAC,KAAK,CAAC,SAAS,CACxB;qBACF;iBACF,CAAC,CAAC;gBACH,OAAO,aAAO,CAAC,IAAI,CACjB,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,aAAa,CAAC,EAC9C,GAAG,EAAE;oBACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBAC7B,MAAM,KAAK,GAAW,EAAE,CAAC;oBACzB,MAAM,cAAc,GAA0B;wBAC5C,cAAc,CACZ,eAAe,CAAC,iBAAiB,EACjC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,EAC7B;4BACE,CAAC,+BAAqB,CAAC,EAAE,sCAA4B;4BACrD,CAAC,uCAA6B,CAAC,EAAE,SAAS;4BAC1C,CAAC,yCAA+B,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK;4BACtD,CAAC,iDAAuC,CAAC,EAAE,MAAM,CAC/C,OAAO,CAAC,KAAK,CAAC,SAAS,CACxB;yBACF,CACF;qBACF,CAAC;oBACF,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;wBACvC,MAAM,iBAAiB,GAAY,iBAAW,CAAC,OAAO,CACpD,kBAAY,EACZ,OAAO,CAAC,OAAO,EACf,gCAAmB,CACpB,CAAC;wBACF,MAAM,WAAW,GAAG,WAAK;6BACtB,OAAO,CAAC,iBAAiB,CAAC;4BAC3B,EAAE,WAAW,EAAE,CAAC;wBAClB,IAAI,YAA8B,CAAC;wBACnC,IAAI,WAAW,EAAE;4BACf,YAAY,GAAG;gCACb,OAAO,EAAE,WAAW;6BACrB,CAAC;yBACH;wBACD,KAAK,CAAC,IAAI,CACR,eAAe,CAAC,kBAAkB,CAAC;4BACjC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK;4BAC1B,OAAO;4BACP,aAAa,EAAE,gDAAsC;4BACrD,IAAI,EAAE,YAAY;4BAClB,UAAU,EAAE;gCACV,CAAC,iDAAuC,CAAC,EAAE,MAAM,CAC/C,OAAO,CAAC,KAAK,CAAC,SAAS,CACxB;6BACF;yBACF,CAAC,CACH,CAAC;wBACF,cAAc,CAAC,IAAI,CACjB,wBAAwB,CACtB,eAAe,CAAC,gBAAgB,EAChC,SAAS,EACT;4BACE,CAAC,+BAAqB,CAAC,EAAE,sCAA4B;4BACrD,CAAC,uCAA6B,CAAC,EAAE,SAAS;4BAC1C,CAAC,yCAA+B,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK;4BACtD,CAAC,iDAAuC,CAAC,EAAE,MAAM,CAC/C,OAAO,CAAC,KAAK,CAAC,SAAS,CACxB;yBACF,CACF,CACF,CAAC;oBACJ,CAAC,CAAC,CAAC;oBACH,MAAM,mBAAmB,GAAkB,QAAS,CAAC,KAAK,CACxD,IAAI,EACJ,IAAI,CACL,CAAC;oBACF,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;oBAC7B,OAAO,eAAe,CAAC,kBAAkB,CACvC,KAAK,EACL,cAAc,EACd,mBAAmB,CACpB,CAAC;gBACJ,CAAC,CACF,CAAC;YACJ,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,0BAA0B;QAChC,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,CAAC,QAA+B,EAAE,EAAE;YACzC,OAAO,SAAS,SAAS,CAEvB,GAAG,IAAuC;gBAE1C,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACtB,MAAM,QAAQ,GAAG,KAAK,CAAC,aAAa,IAAI,EAAE,CAAC;gBAE3C,MAAM,KAAK,GAAW,EAAE,CAAC;gBACzB,MAAM,cAAc,GAA0B,EAAE,CAAC;gBAEjD,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;oBAC9B,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;wBACtC,KAAK,CAAC,IAAI,CACR,eAAe,CAAC,kBAAkB,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAChE,CAAC;wBACF,cAAc,CAAC,IAAI,CACjB,cAAc,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC,EAAE;4BAC/C,CAAC,+BAAqB,CAAC,EAAE,sCAA4B;4BACrD,CAAC,uCAA6B,CAAC,EAAE,MAAM;4BACvC,CAAC,yCAA+B,CAAC,EAAE,YAAY,CAAC,KAAK;4BACrD,GAAG,CAAC,OAAO,CAAC,SAAS,KAAK,SAAS;gCACjC,CAAC,CAAC;oCACE,CAAC,iDAAuC,CAAC,EAAE,MAAM,CAC/C,OAAO,CAAC,SAAS,CAClB;iCACF;gCACH,CAAC,CAAC,EAAE,CAAC;yBACR,CAAC,CACH,CAAC;oBACJ,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,MAAM,cAAc,GAA8B,QAAQ,CAAC,KAAK,CAC9D,IAAI,EACJ,IAAI,CACL,CAAC;gBACF,OAAO,eAAe,CAAC,kBAAkB,CACvC,KAAK,EACL,cAAc,EACd,cAAc,CACf,CAAC;YACJ,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,qBAAqB;QAC3B,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,CAAC,QAA0B,EAAE,EAAE;YACpC,OAAO,SAAS,IAAI,CAElB,GAAG,IAAkC;gBAErC,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACvB,MAAM,KAAK,GAAW,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;oBAClD,OAAO,eAAe,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBACnE,CAAC,CAAC,CAAC;gBAEH,MAAM,cAAc,GAA0B,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CACpE,cAAc,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC,EAAE;oBAC/C,CAAC,+BAAqB,CAAC,EAAE,sCAA4B;oBACrD,CAAC,uCAA6B,CAAC,EAAE,MAAM;oBACvC,CAAC,yCAA+B,CAAC,EAAE,MAAM,CAAC,KAAK;oBAC/C,GAAG,CAAC,CAAC,CAAC,SAAS,KAAK,SAAS;wBAC3B,CAAC,CAAC;4BACE,CAAC,iDAAuC,CAAC,EAAE,MAAM,CAC/C,CAAC,CAAC,SAAS,CACZ;yBACF;wBACH,CAAC,CAAC,EAAE,CAAC;iBACR,CAAC,CACH,CAAC;gBACF,MAAM,cAAc,GAA8B,QAAQ,CAAC,KAAK,CAC9D,IAAI,EACJ,IAAI,CACL,CAAC;gBACF,OAAO,eAAe,CAAC,kBAAkB,CACvC,KAAK,EACL,cAAc,EACd,cAAc,CACf,CAAC;YACJ,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,kBAAkB,CACxB,KAAa,EACb,cAAqC,EACrC,WAAuB;QAEvB,OAAO,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC;aAChC,IAAI,CAAC,MAAM,CAAC,EAAE;YACb,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;YACjC,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC;aACD,KAAK,CAAC,MAAM,CAAC,EAAE;YACd,IAAI,YAAgC,CAAC;YACrC,IAAI,SAAS,GAAW,6CAAsB,CAAC;YAC/C,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,SAAS,EAAE;gBACtD,YAAY,GAAG,MAAM,CAAC;aACvB;iBAAM,IACL,OAAO,MAAM,KAAK,QAAQ;gBAC1B,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,EACvD;gBACA,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC;gBAC9B,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC;aACrC;YACD,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YAE1C,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACnB,IAAI,CAAC,YAAY,CAAC,sCAAe,EAAE,SAAS,CAAC,CAAC;gBAC9C,IAAI,CAAC,SAAS,CAAC;oBACb,IAAI,EAAE,oBAAc,CAAC,KAAK;oBAC1B,OAAO,EAAE,YAAY;iBACtB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,MAAM,MAAM,CAAC;QACf,CAAC,CAAC;aACD,OAAO,CAAC,GAAG,EAAE;YACZ,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,kBAAkB,CAAC,EACzB,KAAK,EACL,OAAO,EACP,aAAa,EACb,GAAG,EACH,IAAI,EACJ,UAAU,GACU;QACpB,MAAM,aAAa,GACjB,aAAa,KAAK,gDAAsC;YACtD,CAAC,CAAC,MAAM,CAAC,6BAA6B;YACtC,CAAC,CAAC,aAAa,CAAC,CAAC,0CAA0C;QAE/D,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAChC,GAAG,aAAa,IAAI,KAAK,EAAE,EAC3B;YACE,IAAI,EACF,aAAa,KAAK,gDAAsC;gBACtD,CAAC,CAAC,cAAQ,CAAC,MAAM;gBACjB,CAAC,CAAC,cAAQ,CAAC,QAAQ;YACvB,UAAU,EAAE;gBACV,GAAG,UAAU;gBACb,CAAC,+BAAqB,CAAC,EAAE,sCAA4B;gBACrD,CAAC,yCAA+B,CAAC,EAAE,KAAK;gBACxC,CAAC,uCAA6B,CAAC,EAAE,aAAa;gBAC9C,CAAC,uCAA6B,CAAC,EAAE,aAAa;gBAC9C,CAAC,0CAAgC,CAAC,EAAE,OAAO,EAAE,GAAG;oBAC9C,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC;oBACrB,CAAC,CAAC,SAAS;gBACb,CAAC,gDAAsC,CAAC,EACtC,OAAO,EAAE,GAAG,IAAI,OAAO,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;gBAC3D,CAAC,qCAA2B,CAAC,EAAE,OAAO,EAAE,MAAM;aAC/C;YACD,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;SAC1B,EACD,GAAG,CACJ,CAAC;QAEF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAC1C,IAAI,YAAY,IAAI,OAAO,EAAE;YAC3B,IAAA,wCAAsB,EACpB,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,EAC5C,CAAC,CAAC,EAAE;gBACF,IAAI,CAAC;oBAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;YACnD,CAAC,EACD,IAAI,CACL,CAAC;SACH;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,kBAAkB,CAAC,KAAa,EAAE,OAAgB;QACxD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,KAAK,EAAE,EAAE;YAClD,IAAI,EAAE,cAAQ,CAAC,QAAQ;YACvB,UAAU,EAAE;gBACV,CAAC,+BAAqB,CAAC,EAAE,sCAA4B;gBACrD,CAAC,yCAA+B,CAAC,EAAE,KAAK;gBACxC,CAAC,0CAAgC,CAAC,EAAE,OAAO,CAAC,GAAG;oBAC7C,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC;oBACrB,CAAC,CAAC,SAAS;gBACb,CAAC,gDAAsC,CAAC,EACtC,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;gBAC1D,CAAC,iDAAuC,CAAC,EACvC,OAAO,CAAC,SAAS,KAAK,SAAS;oBAC7B,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;oBAC3B,CAAC,CAAC,SAAS;gBACf,CAAC,uCAA6B,CAAC,EAAE,MAAM;gBACvC,CAAC,uCAA6B,CAAC,EAAE,6CAAmC;aACrE;SACF,CAAC,CAAC;QAEH,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC;QACxC,iBAAW,CAAC,MAAM,CAAC,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;QAE3E,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAC1C,IAAI,YAAY,EAAE;YAChB,IAAA,wCAAsB,EACpB,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,EAC5C,CAAC,CAAC,EAAE;gBACF,IAAI,CAAC;oBAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;YACnD,CAAC,EACD,IAAI,CACL,CAAC;SACH;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AA1iBD,wDA0iBC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors, <PERSON>pecto\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Attributes,\n  Context,\n  context,\n  Counter,\n  Histogram,\n  Link,\n  propagation,\n  ROOT_CONTEXT,\n  Span,\n  SpanKind,\n  SpanStatusCode,\n  trace,\n} from '@opentelemetry/api';\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  isWrapped,\n  safeExecuteInTheMiddle,\n} from '@opentelemetry/instrumentation';\nimport {\n  ATTR_ERROR_TYPE,\n  ATTR_SERVER_ADDRESS,\n  ATTR_SERVER_PORT,\n  ERROR_TYPE_VALUE_OTHER,\n} from '@opentelemetry/semantic-conventions';\nimport type * as kafkaJs from 'kafkajs';\nimport type {\n  Consumer,\n  ConsumerRunConfig,\n  EachBatchHandler,\n  EachMessageHandler,\n  KafkaMessage,\n  Message,\n  Producer,\n  RecordMetadata,\n} from 'kafkajs';\nimport { EVENT_LISTENERS_SET } from './internal-types';\nimport { bufferTextMapGetter } from './propagator';\nimport {\n  ATTR_MESSAGING_BATCH_MESSAGE_COUNT,\n  ATTR_MESSAGING_DESTINATION_NAME,\n  ATTR_MESSAGING_DESTINATION_PARTITION_ID,\n  ATTR_MESSAGING_KAFKA_MESSAGE_KEY,\n  ATTR_MESSAGING_KAFKA_MESSAGE_TOMBSTONE,\n  ATTR_MESSAGING_KAFKA_OFFSET,\n  ATTR_MESSAGING_OPERATION_NAME,\n  ATTR_MESSAGING_OPERATION_TYPE,\n  ATTR_MESSAGING_SYSTEM,\n  MESSAGING_OPERATION_TYPE_VALUE_PROCESS,\n  MESSAGING_OPERATION_TYPE_VALUE_RECEIVE,\n  MESSAGING_OPERATION_TYPE_VALUE_SEND,\n  MESSAGING_SYSTEM_VALUE_KAFKA,\n  METRIC_MESSAGING_CLIENT_CONSUMED_MESSAGES,\n  METRIC_MESSAGING_CLIENT_OPERATION_DURATION,\n  METRIC_MESSAGING_CLIENT_SENT_MESSAGES,\n  METRIC_MESSAGING_PROCESS_DURATION,\n} from './semconv';\nimport { KafkaJsInstrumentationConfig } from './types';\n/** @knipignore */\nimport { PACKAGE_NAME, PACKAGE_VERSION } from './version';\n\ninterface ConsumerSpanOptions {\n  topic: string;\n  message: KafkaMessage | undefined;\n  operationType: string;\n  attributes: Attributes;\n  ctx?: Context | undefined;\n  link?: Link;\n}\n// This interface acts as a strict subset of the KafkaJS Consumer and\n// Producer interfaces (just for the event we're needing)\ninterface KafkaEventEmitter {\n  on(\n    eventName:\n      | kafkaJs.ConsumerEvents['REQUEST']\n      | kafkaJs.ProducerEvents['REQUEST'],\n    listener: (event: kafkaJs.RequestEvent) => void\n  ): void;\n  events: {\n    REQUEST:\n      | kafkaJs.ConsumerEvents['REQUEST']\n      | kafkaJs.ProducerEvents['REQUEST'];\n  };\n  [EVENT_LISTENERS_SET]?: boolean;\n}\n\ninterface StandardAttributes<OP extends string = string> extends Attributes {\n  [ATTR_MESSAGING_SYSTEM]: string;\n  [ATTR_MESSAGING_OPERATION_NAME]: OP;\n  [ATTR_ERROR_TYPE]?: string;\n}\ninterface TopicAttributes {\n  [ATTR_MESSAGING_DESTINATION_NAME]: string;\n  [ATTR_MESSAGING_DESTINATION_PARTITION_ID]?: string;\n}\n\ninterface ClientDurationAttributes\n  extends StandardAttributes,\n    Partial<TopicAttributes> {\n  [ATTR_SERVER_ADDRESS]: string;\n  [ATTR_SERVER_PORT]: number;\n  [ATTR_MESSAGING_OPERATION_TYPE]?: string;\n}\ninterface SentMessagesAttributes\n  extends StandardAttributes<'send'>,\n    TopicAttributes {\n  [ATTR_ERROR_TYPE]?: string;\n}\ntype ConsumedMessagesAttributes = StandardAttributes<'receive' | 'process'>;\ninterface MessageProcessDurationAttributes\n  extends StandardAttributes<'process'>,\n    TopicAttributes {\n  [ATTR_MESSAGING_SYSTEM]: string;\n  [ATTR_MESSAGING_OPERATION_NAME]: 'process';\n  [ATTR_ERROR_TYPE]?: string;\n}\ntype RecordPendingMetric = (errorType?: string | undefined) => void;\n\nfunction prepareCounter<T extends Attributes>(\n  meter: Counter<T>,\n  value: number,\n  attributes: T\n): RecordPendingMetric {\n  return (errorType?: string | undefined) => {\n    meter.add(value, {\n      ...attributes,\n      ...(errorType ? { [ATTR_ERROR_TYPE]: errorType } : {}),\n    });\n  };\n}\n\nfunction prepareDurationHistogram<T extends Attributes>(\n  meter: Histogram<T>,\n  value: number,\n  attributes: T\n): RecordPendingMetric {\n  return (errorType?: string | undefined) => {\n    meter.record((Date.now() - value) / 1000, {\n      ...attributes,\n      ...(errorType ? { [ATTR_ERROR_TYPE]: errorType } : {}),\n    });\n  };\n}\n\nconst HISTOGRAM_BUCKET_BOUNDARIES = [\n  0.005, 0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1, 2.5, 5, 7.5, 10,\n];\nexport class KafkaJsInstrumentation extends InstrumentationBase<KafkaJsInstrumentationConfig> {\n  private declare _clientDuration: Histogram<ClientDurationAttributes>;\n  private declare _sentMessages: Counter<SentMessagesAttributes>;\n  private declare _consumedMessages: Counter<ConsumedMessagesAttributes>;\n  private declare _processDuration: Histogram<MessageProcessDurationAttributes>;\n\n  constructor(config: KafkaJsInstrumentationConfig = {}) {\n    super(PACKAGE_NAME, PACKAGE_VERSION, config);\n  }\n\n  override _updateMetricInstruments() {\n    this._clientDuration = this.meter.createHistogram(\n      METRIC_MESSAGING_CLIENT_OPERATION_DURATION,\n      { advice: { explicitBucketBoundaries: HISTOGRAM_BUCKET_BOUNDARIES } }\n    );\n    this._sentMessages = this.meter.createCounter(\n      METRIC_MESSAGING_CLIENT_SENT_MESSAGES\n    );\n    this._consumedMessages = this.meter.createCounter(\n      METRIC_MESSAGING_CLIENT_CONSUMED_MESSAGES\n    );\n    this._processDuration = this.meter.createHistogram(\n      METRIC_MESSAGING_PROCESS_DURATION,\n      { advice: { explicitBucketBoundaries: HISTOGRAM_BUCKET_BOUNDARIES } }\n    );\n  }\n\n  protected init() {\n    const unpatch = (moduleExports: typeof kafkaJs) => {\n      if (isWrapped(moduleExports?.Kafka?.prototype.producer)) {\n        this._unwrap(moduleExports.Kafka.prototype, 'producer');\n      }\n      if (isWrapped(moduleExports?.Kafka?.prototype.consumer)) {\n        this._unwrap(moduleExports.Kafka.prototype, 'consumer');\n      }\n    };\n\n    const module = new InstrumentationNodeModuleDefinition(\n      'kafkajs',\n      ['>=0.3.0 <3'],\n      (moduleExports: typeof kafkaJs) => {\n        unpatch(moduleExports);\n        this._wrap(\n          moduleExports?.Kafka?.prototype,\n          'producer',\n          this._getProducerPatch()\n        );\n        this._wrap(\n          moduleExports?.Kafka?.prototype,\n          'consumer',\n          this._getConsumerPatch()\n        );\n\n        return moduleExports;\n      },\n      unpatch\n    );\n    return module;\n  }\n\n  private _getConsumerPatch() {\n    const instrumentation = this;\n    return (original: kafkaJs.Kafka['consumer']) => {\n      return function consumer(\n        this: kafkaJs.Kafka,\n        ...args: Parameters<kafkaJs.Kafka['consumer']>\n      ) {\n        const newConsumer: Consumer = original.apply(this, args);\n\n        if (isWrapped(newConsumer.run)) {\n          instrumentation._unwrap(newConsumer, 'run');\n        }\n\n        instrumentation._wrap(\n          newConsumer,\n          'run',\n          instrumentation._getConsumerRunPatch()\n        );\n\n        instrumentation._setKafkaEventListeners(newConsumer);\n\n        return newConsumer;\n      };\n    };\n  }\n\n  private _setKafkaEventListeners(kafkaObj: KafkaEventEmitter) {\n    if (kafkaObj[EVENT_LISTENERS_SET]) return;\n\n    // The REQUEST Consumer event was added in kafkajs@1.5.0.\n    if (kafkaObj.events?.REQUEST) {\n      kafkaObj.on(\n        kafkaObj.events.REQUEST,\n        this._recordClientDurationMetric.bind(this)\n      );\n    }\n\n    kafkaObj[EVENT_LISTENERS_SET] = true;\n  }\n\n  private _recordClientDurationMetric(\n    event: Pick<kafkaJs.RequestEvent, 'payload'>\n  ) {\n    const [address, port] = event.payload.broker.split(':');\n    this._clientDuration.record(event.payload.duration / 1000, {\n      [ATTR_MESSAGING_SYSTEM]: MESSAGING_SYSTEM_VALUE_KAFKA,\n      [ATTR_MESSAGING_OPERATION_NAME]: `${event.payload.apiName}`, // potentially suffix with @v${event.payload.apiVersion}?\n      [ATTR_SERVER_ADDRESS]: address,\n      [ATTR_SERVER_PORT]: Number.parseInt(port, 10),\n    });\n  }\n\n  private _getProducerPatch() {\n    const instrumentation = this;\n    return (original: kafkaJs.Kafka['producer']) => {\n      return function consumer(\n        this: kafkaJs.Kafka,\n        ...args: Parameters<kafkaJs.Kafka['producer']>\n      ) {\n        const newProducer: Producer = original.apply(this, args);\n\n        if (isWrapped(newProducer.sendBatch)) {\n          instrumentation._unwrap(newProducer, 'sendBatch');\n        }\n        instrumentation._wrap(\n          newProducer,\n          'sendBatch',\n          instrumentation._getProducerSendBatchPatch()\n        );\n\n        if (isWrapped(newProducer.send)) {\n          instrumentation._unwrap(newProducer, 'send');\n        }\n        instrumentation._wrap(\n          newProducer,\n          'send',\n          instrumentation._getProducerSendPatch()\n        );\n\n        instrumentation._setKafkaEventListeners(newProducer);\n\n        return newProducer;\n      };\n    };\n  }\n\n  private _getConsumerRunPatch() {\n    const instrumentation = this;\n    return (original: Consumer['run']) => {\n      return function run(\n        this: Consumer,\n        ...args: Parameters<Consumer['run']>\n      ): ReturnType<Consumer['run']> {\n        const config = args[0];\n        if (config?.eachMessage) {\n          if (isWrapped(config.eachMessage)) {\n            instrumentation._unwrap(config, 'eachMessage');\n          }\n          instrumentation._wrap(\n            config,\n            'eachMessage',\n            instrumentation._getConsumerEachMessagePatch()\n          );\n        }\n        if (config?.eachBatch) {\n          if (isWrapped(config.eachBatch)) {\n            instrumentation._unwrap(config, 'eachBatch');\n          }\n          instrumentation._wrap(\n            config,\n            'eachBatch',\n            instrumentation._getConsumerEachBatchPatch()\n          );\n        }\n        return original.call(this, config);\n      };\n    };\n  }\n\n  private _getConsumerEachMessagePatch() {\n    const instrumentation = this;\n    return (original: ConsumerRunConfig['eachMessage']) => {\n      return function eachMessage(\n        this: unknown,\n        ...args: Parameters<EachMessageHandler>\n      ): Promise<void> {\n        const payload = args[0];\n        const propagatedContext: Context = propagation.extract(\n          ROOT_CONTEXT,\n          payload.message.headers,\n          bufferTextMapGetter\n        );\n        const span = instrumentation._startConsumerSpan({\n          topic: payload.topic,\n          message: payload.message,\n          operationType: MESSAGING_OPERATION_TYPE_VALUE_PROCESS,\n          ctx: propagatedContext,\n          attributes: {\n            [ATTR_MESSAGING_DESTINATION_PARTITION_ID]: String(\n              payload.partition\n            ),\n          },\n        });\n\n        const pendingMetrics: RecordPendingMetric[] = [\n          prepareDurationHistogram(\n            instrumentation._processDuration,\n            Date.now(),\n            {\n              [ATTR_MESSAGING_SYSTEM]: MESSAGING_SYSTEM_VALUE_KAFKA,\n              [ATTR_MESSAGING_OPERATION_NAME]: 'process',\n              [ATTR_MESSAGING_DESTINATION_NAME]: payload.topic,\n              [ATTR_MESSAGING_DESTINATION_PARTITION_ID]: String(\n                payload.partition\n              ),\n            }\n          ),\n          prepareCounter(instrumentation._consumedMessages, 1, {\n            [ATTR_MESSAGING_SYSTEM]: MESSAGING_SYSTEM_VALUE_KAFKA,\n            [ATTR_MESSAGING_OPERATION_NAME]: 'process',\n            [ATTR_MESSAGING_DESTINATION_NAME]: payload.topic,\n            [ATTR_MESSAGING_DESTINATION_PARTITION_ID]: String(\n              payload.partition\n            ),\n          }),\n        ];\n\n        const eachMessagePromise = context.with(\n          trace.setSpan(propagatedContext, span),\n          () => {\n            return original!.apply(this, args);\n          }\n        );\n        return instrumentation._endSpansOnPromise(\n          [span],\n          pendingMetrics,\n          eachMessagePromise\n        );\n      };\n    };\n  }\n\n  private _getConsumerEachBatchPatch() {\n    return (original: ConsumerRunConfig['eachBatch']) => {\n      const instrumentation = this;\n      return function eachBatch(\n        this: unknown,\n        ...args: Parameters<EachBatchHandler>\n      ): Promise<void> {\n        const payload = args[0];\n        // https://github.com/open-telemetry/opentelemetry-specification/blob/master/specification/trace/semantic_conventions/messaging.md#topic-with-multiple-consumers\n        const receivingSpan = instrumentation._startConsumerSpan({\n          topic: payload.batch.topic,\n          message: undefined,\n          operationType: MESSAGING_OPERATION_TYPE_VALUE_RECEIVE,\n          ctx: ROOT_CONTEXT,\n          attributes: {\n            [ATTR_MESSAGING_BATCH_MESSAGE_COUNT]: payload.batch.messages.length,\n            [ATTR_MESSAGING_DESTINATION_PARTITION_ID]: String(\n              payload.batch.partition\n            ),\n          },\n        });\n        return context.with(\n          trace.setSpan(context.active(), receivingSpan),\n          () => {\n            const startTime = Date.now();\n            const spans: Span[] = [];\n            const pendingMetrics: RecordPendingMetric[] = [\n              prepareCounter(\n                instrumentation._consumedMessages,\n                payload.batch.messages.length,\n                {\n                  [ATTR_MESSAGING_SYSTEM]: MESSAGING_SYSTEM_VALUE_KAFKA,\n                  [ATTR_MESSAGING_OPERATION_NAME]: 'process',\n                  [ATTR_MESSAGING_DESTINATION_NAME]: payload.batch.topic,\n                  [ATTR_MESSAGING_DESTINATION_PARTITION_ID]: String(\n                    payload.batch.partition\n                  ),\n                }\n              ),\n            ];\n            payload.batch.messages.forEach(message => {\n              const propagatedContext: Context = propagation.extract(\n                ROOT_CONTEXT,\n                message.headers,\n                bufferTextMapGetter\n              );\n              const spanContext = trace\n                .getSpan(propagatedContext)\n                ?.spanContext();\n              let origSpanLink: Link | undefined;\n              if (spanContext) {\n                origSpanLink = {\n                  context: spanContext,\n                };\n              }\n              spans.push(\n                instrumentation._startConsumerSpan({\n                  topic: payload.batch.topic,\n                  message,\n                  operationType: MESSAGING_OPERATION_TYPE_VALUE_PROCESS,\n                  link: origSpanLink,\n                  attributes: {\n                    [ATTR_MESSAGING_DESTINATION_PARTITION_ID]: String(\n                      payload.batch.partition\n                    ),\n                  },\n                })\n              );\n              pendingMetrics.push(\n                prepareDurationHistogram(\n                  instrumentation._processDuration,\n                  startTime,\n                  {\n                    [ATTR_MESSAGING_SYSTEM]: MESSAGING_SYSTEM_VALUE_KAFKA,\n                    [ATTR_MESSAGING_OPERATION_NAME]: 'process',\n                    [ATTR_MESSAGING_DESTINATION_NAME]: payload.batch.topic,\n                    [ATTR_MESSAGING_DESTINATION_PARTITION_ID]: String(\n                      payload.batch.partition\n                    ),\n                  }\n                )\n              );\n            });\n            const batchMessagePromise: Promise<void> = original!.apply(\n              this,\n              args\n            );\n            spans.unshift(receivingSpan);\n            return instrumentation._endSpansOnPromise(\n              spans,\n              pendingMetrics,\n              batchMessagePromise\n            );\n          }\n        );\n      };\n    };\n  }\n\n  private _getProducerSendBatchPatch() {\n    const instrumentation = this;\n    return (original: Producer['sendBatch']) => {\n      return function sendBatch(\n        this: Producer,\n        ...args: Parameters<Producer['sendBatch']>\n      ): ReturnType<Producer['sendBatch']> {\n        const batch = args[0];\n        const messages = batch.topicMessages || [];\n\n        const spans: Span[] = [];\n        const pendingMetrics: RecordPendingMetric[] = [];\n\n        messages.forEach(topicMessage => {\n          topicMessage.messages.forEach(message => {\n            spans.push(\n              instrumentation._startProducerSpan(topicMessage.topic, message)\n            );\n            pendingMetrics.push(\n              prepareCounter(instrumentation._sentMessages, 1, {\n                [ATTR_MESSAGING_SYSTEM]: MESSAGING_SYSTEM_VALUE_KAFKA,\n                [ATTR_MESSAGING_OPERATION_NAME]: 'send',\n                [ATTR_MESSAGING_DESTINATION_NAME]: topicMessage.topic,\n                ...(message.partition !== undefined\n                  ? {\n                      [ATTR_MESSAGING_DESTINATION_PARTITION_ID]: String(\n                        message.partition\n                      ),\n                    }\n                  : {}),\n              })\n            );\n          });\n        });\n        const origSendResult: Promise<RecordMetadata[]> = original.apply(\n          this,\n          args\n        );\n        return instrumentation._endSpansOnPromise(\n          spans,\n          pendingMetrics,\n          origSendResult\n        );\n      };\n    };\n  }\n\n  private _getProducerSendPatch() {\n    const instrumentation = this;\n    return (original: Producer['send']) => {\n      return function send(\n        this: Producer,\n        ...args: Parameters<Producer['send']>\n      ): ReturnType<Producer['send']> {\n        const record = args[0];\n        const spans: Span[] = record.messages.map(message => {\n          return instrumentation._startProducerSpan(record.topic, message);\n        });\n\n        const pendingMetrics: RecordPendingMetric[] = record.messages.map(m =>\n          prepareCounter(instrumentation._sentMessages, 1, {\n            [ATTR_MESSAGING_SYSTEM]: MESSAGING_SYSTEM_VALUE_KAFKA,\n            [ATTR_MESSAGING_OPERATION_NAME]: 'send',\n            [ATTR_MESSAGING_DESTINATION_NAME]: record.topic,\n            ...(m.partition !== undefined\n              ? {\n                  [ATTR_MESSAGING_DESTINATION_PARTITION_ID]: String(\n                    m.partition\n                  ),\n                }\n              : {}),\n          })\n        );\n        const origSendResult: Promise<RecordMetadata[]> = original.apply(\n          this,\n          args\n        );\n        return instrumentation._endSpansOnPromise(\n          spans,\n          pendingMetrics,\n          origSendResult\n        );\n      };\n    };\n  }\n\n  private _endSpansOnPromise<T>(\n    spans: Span[],\n    pendingMetrics: RecordPendingMetric[],\n    sendPromise: Promise<T>\n  ): Promise<T> {\n    return Promise.resolve(sendPromise)\n      .then(result => {\n        pendingMetrics.forEach(m => m());\n        return result;\n      })\n      .catch(reason => {\n        let errorMessage: string | undefined;\n        let errorType: string = ERROR_TYPE_VALUE_OTHER;\n        if (typeof reason === 'string' || reason === undefined) {\n          errorMessage = reason;\n        } else if (\n          typeof reason === 'object' &&\n          Object.prototype.hasOwnProperty.call(reason, 'message')\n        ) {\n          errorMessage = reason.message;\n          errorType = reason.constructor.name;\n        }\n        pendingMetrics.forEach(m => m(errorType));\n\n        spans.forEach(span => {\n          span.setAttribute(ATTR_ERROR_TYPE, errorType);\n          span.setStatus({\n            code: SpanStatusCode.ERROR,\n            message: errorMessage,\n          });\n        });\n\n        throw reason;\n      })\n      .finally(() => {\n        spans.forEach(span => span.end());\n      });\n  }\n\n  private _startConsumerSpan({\n    topic,\n    message,\n    operationType,\n    ctx,\n    link,\n    attributes,\n  }: ConsumerSpanOptions) {\n    const operationName =\n      operationType === MESSAGING_OPERATION_TYPE_VALUE_RECEIVE\n        ? 'poll' // for batch processing spans\n        : operationType; // for individual message processing spans\n\n    const span = this.tracer.startSpan(\n      `${operationName} ${topic}`,\n      {\n        kind:\n          operationType === MESSAGING_OPERATION_TYPE_VALUE_RECEIVE\n            ? SpanKind.CLIENT\n            : SpanKind.CONSUMER,\n        attributes: {\n          ...attributes,\n          [ATTR_MESSAGING_SYSTEM]: MESSAGING_SYSTEM_VALUE_KAFKA,\n          [ATTR_MESSAGING_DESTINATION_NAME]: topic,\n          [ATTR_MESSAGING_OPERATION_TYPE]: operationType,\n          [ATTR_MESSAGING_OPERATION_NAME]: operationName,\n          [ATTR_MESSAGING_KAFKA_MESSAGE_KEY]: message?.key\n            ? String(message.key)\n            : undefined,\n          [ATTR_MESSAGING_KAFKA_MESSAGE_TOMBSTONE]:\n            message?.key && message.value === null ? true : undefined,\n          [ATTR_MESSAGING_KAFKA_OFFSET]: message?.offset,\n        },\n        links: link ? [link] : [],\n      },\n      ctx\n    );\n\n    const { consumerHook } = this.getConfig();\n    if (consumerHook && message) {\n      safeExecuteInTheMiddle(\n        () => consumerHook(span, { topic, message }),\n        e => {\n          if (e) this._diag.error('consumerHook error', e);\n        },\n        true\n      );\n    }\n\n    return span;\n  }\n\n  private _startProducerSpan(topic: string, message: Message) {\n    const span = this.tracer.startSpan(`send ${topic}`, {\n      kind: SpanKind.PRODUCER,\n      attributes: {\n        [ATTR_MESSAGING_SYSTEM]: MESSAGING_SYSTEM_VALUE_KAFKA,\n        [ATTR_MESSAGING_DESTINATION_NAME]: topic,\n        [ATTR_MESSAGING_KAFKA_MESSAGE_KEY]: message.key\n          ? String(message.key)\n          : undefined,\n        [ATTR_MESSAGING_KAFKA_MESSAGE_TOMBSTONE]:\n          message.key && message.value === null ? true : undefined,\n        [ATTR_MESSAGING_DESTINATION_PARTITION_ID]:\n          message.partition !== undefined\n            ? String(message.partition)\n            : undefined,\n        [ATTR_MESSAGING_OPERATION_NAME]: 'send',\n        [ATTR_MESSAGING_OPERATION_TYPE]: MESSAGING_OPERATION_TYPE_VALUE_SEND,\n      },\n    });\n\n    message.headers = message.headers ?? {};\n    propagation.inject(trace.setSpan(context.active(), span), message.headers);\n\n    const { producerHook } = this.getConfig();\n    if (producerHook) {\n      safeExecuteInTheMiddle(\n        () => producerHook(span, { topic, message }),\n        e => {\n          if (e) this._diag.error('producerHook error', e);\n        },\n        true\n      );\n    }\n\n    return span;\n  }\n}\n"]}