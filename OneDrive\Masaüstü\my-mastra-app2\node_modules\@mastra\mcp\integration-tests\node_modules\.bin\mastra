#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/work/mastra/mastra/packages/cli/dist/node_modules:/home/<USER>/work/mastra/mastra/packages/cli/node_modules:/home/<USER>/work/mastra/mastra/packages/node_modules:/home/<USER>/work/mastra/mastra/node_modules:/home/<USER>/work/mastra/node_modules:/home/<USER>/work/node_modules:/home/<USER>/node_modules:/home/<USER>/node_modules:/home/<USER>/work/mastra/mastra/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/work/mastra/mastra/packages/cli/dist/node_modules:/home/<USER>/work/mastra/mastra/packages/cli/node_modules:/home/<USER>/work/mastra/mastra/packages/node_modules:/home/<USER>/work/mastra/mastra/node_modules:/home/<USER>/work/mastra/node_modules:/home/<USER>/work/node_modules:/home/<USER>/node_modules:/home/<USER>/node_modules:/home/<USER>/work/mastra/mastra/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../mastra/dist/index.js" "$@"
else
  exec node  "$basedir/../mastra/dist/index.js" "$@"
fi
