{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Span } from '@opentelemetry/api';\nimport { InstrumentationConfig } from '@opentelemetry/instrumentation';\n\nexport interface FastifyRequestInfo {\n  request: any; // FastifyRequest object from fastify package\n}\n\n/**\n * Function that can be used to add custom attributes to the current span\n * @param span - The Fastify handler span.\n * @param info - The Fastify request info object.\n */\nexport interface FastifyCustomAttributeFunction {\n  (span: Span, info: FastifyRequestInfo): void;\n}\n\n/**\n * Options available for the Fastify Instrumentation\n */\nexport interface FastifyInstrumentationConfig extends InstrumentationConfig {\n  /** Function for adding custom attributes to each handler span */\n  requestHook?: FastifyCustomAttributeFunction;\n}\n"]}