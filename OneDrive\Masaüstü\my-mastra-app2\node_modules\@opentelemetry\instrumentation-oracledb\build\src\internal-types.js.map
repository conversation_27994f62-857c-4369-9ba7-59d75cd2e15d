{"version": 3, "file": "internal-types.js", "sourceRoot": "", "sources": ["../../src/internal-types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;KAgBK", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * Copyright (c) 2025, Oracle and/or its affiliates.\n * */\n\nimport type * as oracledbTypes from 'oracledb';\nimport type * as api from '@opentelemetry/api';\nimport { SpanConnectionConfig } from './types';\n\n// onEnterFn returns this Context(contains only span for now) and it is\n// received in onExitFn to end the span.\nexport interface InstrumentationContext {\n  span: api.Span;\n}\n\n// Captures the entire span data.\n// This corresponds to js object filled by the 'oracledb' module\n// See: https://github.com/oracle/node-oracledb/blob/main/lib/traceHandler.js\nexport interface TraceSpanData {\n  operation: string; // RPC or exported function name.\n  error?: oracledbTypes.DBError;\n  connectLevelConfig: SpanConnectionConfig;\n  callLevelConfig?: SpanCallLevelConfig;\n  additionalConfig?: any; // custom key/values associated with a function.\n  fn: Function; // Replaced with bind function associating the active context.\n  args?: any[]; // input arguments passed to the exported function.\n\n  /**\n   * This value is filled by instrumented module inside 'onEnterFn',\n   * 'onBeginRoundTrip' hook functions, which is passed back by oracledb module\n   * in 'onExitFn' and 'onEndRoundTrip' hook functions respectively.\n   */\n  userContext: InstrumentationContext;\n}\n\n// Captures call level related span data\nexport interface SpanCallLevelConfig {\n  statement?: string; // SQL stmt.\n  operation?: string; // SQL op ('SELECT | INSERT ..').\n  values?: any[]; // bind values.\n}\n"]}