{"version": 3, "file": "semconv.js", "sourceRoot": "", "sources": ["../../src/semconv.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;KAgBK;;;AAEL;;;;GAIG;AACU,QAAA,cAAc,GAAG,gBAAgB,CAAC;AAE/C;;;;;;;;;;;;;GAaG;AACU,QAAA,iBAAiB,GAAG,cAAc,CAAC;AAEhD;;;;;;;;;;;;;GAaG;AACU,QAAA,sBAAsB,GAAG,mBAAmB,CAAC;AAE1D;;;;;;;;;;;;;;;;;;;;GAoBG;AACU,QAAA,iBAAiB,GAAG,eAAe,CAAC;AAEjD;;;;;;;;;;;;;GAaG;AACU,QAAA,2BAA2B,GAAG,wBAAwB,CAAC;AAEpE;;;GAGG;AACU,QAAA,YAAY,GAAG,SAAS,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * Copyright (c) 2025, Oracle and/or its affiliates.\n * */\n\n/**\n * The database management system (DBMS) product as identified\n * by the client instrumentation.\n *\n */\nexport const ATTR_DB_SYSTEM = 'db.system.name';\n\n/**\n * The database associated with the connection, qualified by the instance name, database name and service name.\n *\n * @example ORCL1|PDB1|db_high.adb.oraclecloud.com\n * @example ORCL1|DB1|db_low.adb.oraclecloud.com\n *\n * @note It **SHOULD** be set to the combination of instance name, database name and\n * service name following the `{instance_name}|{database_name}|{service_name}` pattern.\n * For CDB architecture, database name would be pdb name. For Non-CDB, it would be\n * **DB_NAME** parameter.\n * This attribute has stability level RELEASE CANDIDATE.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DB_NAMESPACE = 'db.namespace';\n\n/**\n * The name of the operation or command being executed.\n *\n * @example INSERT\n * @example COMMIT\n * @example SELECT\n *\n * @note It is **RECOMMENDED** to capture the value as provided by the application without attempting to do any case normalization.\n * If the operation name is parsed from the query text, it **SHOULD** be the first operation name found in the query.\n * For batch operations, if the individual operations are known to have the same operation name then that operation name **SHOULD** be used prepended by `BATCH `, otherwise `db.operation.name` **SHOULD** be `BATCH` or some other database system specific term if more applicable.\n * This attribute has stability level RELEASE CANDIDATE.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DB_OPERATION_NAME = 'db.operation.name';\n\n/**\n * The database query being executed.\n *\n * @example SELECT * FROM wuser_table where username = :1 // bind by position\n * @example SELECT * FROM wuser_table where username = :name // bind by name\n * @example SELECT * FROM wuser_table where username = 'John' // literals\n *\n * @note For sanitization see [Sanitization of `db.query.text`](../database/database-spans.md#sanitization-of-dbquerytext).\n * For batch operations, if the individual operations are known to have the same query text then\n * that query text **SHOULD** be used, otherwise all of the individual query texts **SHOULD**\n * be concatenated with separator `; ` or some other database system specific separator if more applicable.\n *\n * Non-parameterized or Parameterized query text **SHOULD NOT** be collected by default unless\n * explicitly configured and sanitized to exclude sensitive data, e.g. by redacting all\n * literal values present in the query text. See Sanitization of `db.query.text`.\n *\n * Parameterized query text MUST also NOT be collected by default unless explicitly configured.\n * The query parameter values themselves are opt-in, see [`db.operation.parameter.<key>`](../attributes-registry/db.md))\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DB_STATEMENT = 'db.query.text';\n\n/**\n * A database operation parameter, with <key> being the parameter name,\n * and the attribute value being a string representation of the parameter value.\n *\n * @example someval\n * @example 55\n *\n * @note  If a parameter has no name and instead is referenced only by index, then\n * <key> **SHOULD** be the 0-based index. If `db.query.text` is also captured, then\n * `db.operation.parameter.<key>` **SHOULD** match up with the parameterized placeholders\n * present in db.query.text\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DB_OPERATION_PARAMETER = 'db.operation.parameter';\n\n/**\n * Username for accessing the database.\n *\n */\nexport const ATTR_DB_USER = 'db.user';\n"]}